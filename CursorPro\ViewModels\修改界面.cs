using CursorPro.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace CursorPro.ViewModels
{
    /// <summary>
    /// 修改界面UI
    /// </summary>
    public class 界面修改器
    {
        // 定义表情符号常量
        private static readonly Dictionary<string, string> EMOJI = new Dictionary<string, string>
        {
            { "FILE", "📄" },
            { "BACKUP", "💾" },
            { "SUCCESS", "✅" },
            { "ERROR", "❌" },
            { "INFO", "ℹ️" },
            { "RESET", "🔄" },
            { "WARNING", "⚠️" }
        };

        /// <summary>
        /// 获取用户文档文件夹路径
        /// </summary>
        /// <returns>文档目录的路径</returns>
        private static string 获取用户文档路径()
        {
            // 根据系统返回文档文件夹路径
            return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        }

        /// <summary>
        /// 获取Cursor workbench.desktop.main.js文件路径
        /// </summary>
        /// <returns>主JS文件路径</returns>
        public static string 获取Cursor主文件路径()
        {
            string 系统类型 = Info.平台类型;

            // 读取配置
            string 配置目录 = Path.Combine(获取用户文档路径(), ".cursor-free-everyday");
            string 配置文件 = Path.Combine(配置目录, "config.ini");

            // 定义不同系统的路径
            Dictionary<string, Dictionary<string, string>> 路径映射 = new Dictionary<string, Dictionary<string, string>>
            {
                {
                    "Mac", new Dictionary<string, string>
                    {
                        { "base", "/Applications/Cursor.app/Contents/Resources/app" },
                        { "main", "out/vs/workbench/workbench.desktop.main.js" }
                    }
                },
                {
                    "Windows", new Dictionary<string, string>
                    {
                        { "main", "out\\vs\\workbench\\workbench.desktop.main.js" }
                    }
                },

            };

            // 验证系统类型是否支持
            if (!路径映射.ContainsKey(系统类型))
            {
                throw new PlatformNotSupportedException($"不支持的操作系统: {系统类型}");
            }

            // 确定基础路径
            string 基础路径;
            if (系统类型 == "Windows")
            {
                // 在Windows上，从注册表或已知目录查找Cursor安装路径
                基础路径 = 查找Windows版Cursor安装路径();

                if (string.IsNullOrEmpty(基础路径))
                {
                    // 尝试从配置文件中读取
                    基础路径 = LocalConfig.读取("cursor_path") ?? string.Empty;

                    if (string.IsNullOrEmpty(基础路径))
                    {
                        throw new FileNotFoundException("无法找到Cursor安装目录，请手动指定路径");
                    }
                }
            }
            else if (系统类型 == "Mac")
            {
                基础路径 = 路径映射[系统类型]["base"];

                // 尝试从配置读取覆盖默认路径
                string? 已保存路径 = LocalConfig.读取("cursor_path");
                if (!string.IsNullOrEmpty(已保存路径))
                {
                    基础路径 = 已保存路径;
                }
            }

            else
            {
                throw new PlatformNotSupportedException($"不支持的操作系统: {系统类型}");
            }

            // 拼接完整主文件路径
            string 主文件路径 = Path.Combine(基础路径, 路径映射[系统类型]["main"]);

            // 验证文件是否存在
            if (!File.Exists(主文件路径))
            {
                throw new FileNotFoundException($"未找到Cursor主文件: {主文件路径}");
            }

            return 主文件路径;
        }

        /// <summary>
        /// 查找Windows版Cursor的安装路径
        /// </summary>
        /// <returns>安装目录路径</returns>
        private static string 查找Windows版Cursor安装路径()
        {
            try
            {
                // 直接从LocalConfig读取cursorPath
                string? cursorExePath = LocalConfig.读取("cursorPath");
                if (string.IsNullOrEmpty(cursorExePath) || !File.Exists(cursorExePath))
                {
                    throw new FileNotFoundException("无法找到Cursor主程序路径，请先启动Cursor后再试");
                }
                // 获取Cursor.exe所在目录（即Cursor安装目录）
                string? exeDir = Path.GetDirectoryName(cursorExePath) ?? string.Empty;
                if (string.IsNullOrEmpty(exeDir))
                {
                    throw new DirectoryNotFoundException("无法解析Cursor主程序目录");
                }
                // resources/app目录应该在Cursor.exe所在目录下
                string resourcesAppPath = Path.Combine(exeDir, "resources", "app");

                if (Directory.Exists(resourcesAppPath))
                {
                    Debug.WriteLine($"已找到Cursor资源路径: {resourcesAppPath}");
                    return resourcesAppPath;
                }
                else
                {
                    throw new DirectoryNotFoundException($"未找到Cursor资源目录: {resourcesAppPath}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查找Cursor安装路径时出错: {ex.Message}");

                // 如果上面的方法失败，尝试使用默认安装路径
                string[] 可能路径 = new string[]
                {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Programs", "Cursor", "resources", "app"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "Cursor", "resources", "app"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "Cursor", "resources", "app")
                };

                foreach (string 路径 in 可能路径)
                {
                    if (Directory.Exists(路径))
                    {
                        Debug.WriteLine($"找到Cursor安装路径: {路径}");
                        return 路径;
                    }
                }

                Debug.WriteLine("未找到Cursor安装路径");
                return string.Empty;
            }
        }



        /// <summary>
        /// 修改workbench文件内容
        /// </summary>
        /// <param name="文件路径">JS文件路径</param>
        /// <returns>是否修改成功</returns>
        public static bool 修改Workbench文件(string 文件路径)
        {
            try
            {
                // 保存原始文件权限
                var 原始权限 = File.GetAttributes(文件路径);

                // 创建临时文件
                string 临时文件路径 = Path.GetTempFileName();

                try
                {
                    // 读取原始内容
                    string 原始内容;
                    using (var 读取流 = new StreamReader(文件路径, Encoding.UTF8))
                    {
                        原始内容 = 读取流.ReadToEnd();
                    }

                    // 使用patterns进行替换，并添加调试信息
                    string 新内容 = 原始内容;

                    // 使用通用的模式匹配方式，不再区分具体版本
                    // 查找包含"title:"Upgrade to Pro""的部分
                    string 关键特征 = "title:\"Upgrade to Pro\"";
                    int 位置 = 新内容.IndexOf(关键特征);

                    if (位置 >= 0)
                    {
                        Debug.WriteLine($"{EMOJI["SUCCESS"]} 找到\"Upgrade to Pro\"按钮");

                        // 替换为"Business Pro"
                        新内容 = 新内容.Replace(关键特征, "title:\"Business Pro\"");
                        Debug.WriteLine($"{EMOJI["SUCCESS"]} 已将\"Upgrade to Pro\"替换为\"Business Pro\"");
                    }
                    else
                    {
                        Debug.WriteLine($"{EMOJI["WARNING"]} 未找到\"Upgrade to Pro\"按钮");
                    }

                    // 处理其他替换模式
                    Dictionary<string, string> 替换模式 = new Dictionary<string, string>
                    {
                        // Badge 替换
                        {"<div>Pro Trial", "<div>Pro"},

                        // Toast 替换 隐藏通知弹窗
                        {"notifications-toasts", "notifications-toasts hidden"}
                    };

                    foreach (var 替换 in 替换模式)
                    {
                        string 旧模式 = 替换.Key;
                        string 新模式 = 替换.Value;

                        // 检查是否包含当前模式
                        bool 找到模式 = 新内容.Contains(旧模式);
                        if (找到模式)
                        {
                            Debug.WriteLine($"{EMOJI["SUCCESS"]} 找到匹配模式: {旧模式.Substring(0, Math.Min(30, 旧模式.Length))}...");

                            // 执行替换
                            新内容 = 新内容.Replace(旧模式, 新模式);

                            // 检查是否有替换发生
                            if (新内容 != 原始内容)
                            {
                                Debug.WriteLine($"{EMOJI["SUCCESS"]} 成功替换模式: {旧模式.Substring(0, Math.Min(30, 旧模式.Length))}...");
                            }
                            else
                            {
                                Debug.WriteLine($"{EMOJI["ERROR"]} 找到模式但替换失败: {旧模式.Substring(0, Math.Min(30, 旧模式.Length))}...");
                            }
                        }
                        else
                        {
                            Debug.WriteLine($"{EMOJI["WARNING"]} 未找到匹配模式: {旧模式.Substring(0, Math.Min(30, 旧模式.Length))}...");
                        }
                    }

                    // 写入临时文件
                    using (var 写入流 = new StreamWriter(临时文件路径, false, Encoding.UTF8))
                    {
                        写入流.Write(新内容);
                    }

                    // 备份原始文件(带时间戳)
                    string 时间戳 = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    string 备份路径 = $"{文件路径}.backup.{时间戳}";
                    File.Copy(文件路径, 备份路径, true);
                    Debug.WriteLine($"{EMOJI["SUCCESS"]} 已创建备份: {备份路径}");

                    // 删除原始文件并移动临时文件
                    if (File.Exists(文件路径))
                    {
                        File.Delete(文件路径);
                    }
                    File.Move(临时文件路径, 文件路径);

                    // 恢复原始权限
                    File.SetAttributes(文件路径, 原始权限);

                    Debug.WriteLine($"{EMOJI["SUCCESS"]} 文件修改成功");
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"{EMOJI["ERROR"]} 修改文件失败: {ex.Message}");

                    // 清理临时文件
                    if (File.Exists(临时文件路径))
                    {
                        try
                        {
                            File.Delete(临时文件路径);
                        }
                        catch { }
                    }

                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"{EMOJI["ERROR"]} 修改文件过程中出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行修改界面UI
        /// </summary>
        /// <returns>操作结果</returns>
        public static bool 执行修改界面()
        {
            try
            {
                Debug.WriteLine($"{EMOJI["RESET"]} 开始执行修改界面UI...");

                // 获取主文件路径
                string 主文件路径 = 获取Cursor主文件路径();

                // 修改文件
                bool 修改成功 = 修改Workbench文件(主文件路径);

                if (修改成功)
                {
                    Debug.WriteLine($"{EMOJI["SUCCESS"]} 修改界面UI完成，请重启Cursor应用");
                    return true;
                }
                else
                {
                    Debug.WriteLine($"{EMOJI["ERROR"]} 修改界面UI失败");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"{EMOJI["ERROR"]} 执行修改界面UI过程中出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 为重置机器码类提供的执行接口
        /// </summary>
        /// <returns>操作是否成功</returns>
        public static Task<bool> 从重置机器码调用()
        {
            Debug.WriteLine("从重置机器码调用修改界面UI功能...");
            return Task.FromResult(执行修改界面());
        }
    }
}