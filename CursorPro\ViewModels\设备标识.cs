using CursorPro.Models;
using System;
using System.Diagnostics;
using System.Linq;
using System.Net.NetworkInformation;

namespace CursorPro.ViewModels
{
    /// <summary>
    /// 获取设备标识类，用于生成唯一的机器码
    /// </summary>
    public class 获取设备标识
    {
        private string _机器码 = string.Empty;

        // 使用一个不明显的键名来存储机器码
        private const string 配置键名 = "app_instance_data";

        /// <summary>
        /// 获取机器码 - 返回设备的唯一标识
        /// </summary>
        /// <returns>机器码</returns>
        public string 获取机器码()
        {
            // 如果已经获取过机器码，直接返回缓存的值
            if (!string.IsNullOrEmpty(_机器码))
            {
                return _机器码;
            }

            // 首先尝试获取真实的物理以太网适配器MAC地址
            _机器码 = 获取物理MAC地址();

            if (!string.IsNullOrEmpty(_机器码))
            {
                Debug.WriteLine($"获取到物理MAC地址作为设备标识: {_机器码}");
            }
            else
            {
                // 如果获取不到真实MAC地址，从本地配置文件读取
                string? 已保存机器码 = LocalConfig.读取(配置键名);
                if (!string.IsNullOrEmpty(已保存机器码))
                {
                    _机器码 = 已保存机器码;
                    Debug.WriteLine($"无法获取物理MAC地址，从配置文件读取设备标识: {_机器码}");
                }
                else
                {
                    // 如果本地也没有，则生成新的随机机器码
                    _机器码 = 生成随机机器码();
                    Debug.WriteLine($"无法获取物理MAC地址且本地无保存，生成新的随机设备标识: {_机器码}");
                }
            }

            // 保存到配置文件（无论是MAC地址还是随机生成的）
            LocalConfig.写入(配置键名, _机器码);

            // 保存机器码
            Info.机器码 = _机器码;
            return _机器码;
        }

        /// <summary>
        /// 获取物理以太网适配器的MAC地址作为机器码
        /// </summary>
        /// <returns>MAC地址字符串，如果获取失败返回空字符串</returns>
        private string 获取物理MAC地址()
        {
            try
            {
                // 获取所有网络接口
                var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();

                // 筛选物理以太网适配器
                var ethernetInterface = networkInterfaces
                    .Where(ni => ni.NetworkInterfaceType == NetworkInterfaceType.Ethernet &&
                                ni.OperationalStatus == OperationalStatus.Up &&
                                !ni.Description.Contains("virtual", StringComparison.OrdinalIgnoreCase) &&
                                !ni.Description.Contains("vmware", StringComparison.OrdinalIgnoreCase) &&
                                !ni.Description.Contains("virtualbox", StringComparison.OrdinalIgnoreCase) &&
                                !ni.Description.Contains("hyper-v", StringComparison.OrdinalIgnoreCase) &&
                                !ni.Description.Contains("loopback", StringComparison.OrdinalIgnoreCase))
                    .FirstOrDefault();

                if (ethernetInterface != null)
                {
                    // 获取MAC地址并格式化
                    var macAddress = ethernetInterface.GetPhysicalAddress().ToString();
                    if (!string.IsNullOrEmpty(macAddress) && macAddress != "000000000000")
                    {
                        // 格式化MAC地址为标准格式 (去掉分隔符)
                        return macAddress.ToUpper();
                    }
                }

                // 如果没有找到合适的以太网适配器，尝试其他类型的网络接口
                var anyInterface = networkInterfaces
                    .Where(ni => ni.OperationalStatus == OperationalStatus.Up &&
                                !ni.Description.Contains("virtual", StringComparison.OrdinalIgnoreCase) &&
                                !ni.Description.Contains("vmware", StringComparison.OrdinalIgnoreCase) &&
                                !ni.Description.Contains("virtualbox", StringComparison.OrdinalIgnoreCase) &&
                                !ni.Description.Contains("hyper-v", StringComparison.OrdinalIgnoreCase) &&
                                !ni.Description.Contains("loopback", StringComparison.OrdinalIgnoreCase))
                    .FirstOrDefault();

                if (anyInterface != null)
                {
                    var macAddress = anyInterface.GetPhysicalAddress().ToString();
                    if (!string.IsNullOrEmpty(macAddress) && macAddress != "000000000000")
                    {
                        return macAddress.ToUpper();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取MAC地址失败: {ex.Message}");
            }

            return string.Empty;
        }

        /// <summary>
        /// 生成10位随机机器码，对于几万客户规模已经足够
        /// </summary>
        private static string 生成随机机器码()
        {
            var random = new Random();
            var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            return new string(Enumerable.Repeat(chars, 10)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }
}