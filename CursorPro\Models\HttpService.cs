using System;
using System.Diagnostics;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace CursorPro.Models
{
    /// <summary>
    /// HTTP服务类，封装HTTP请求操作
    /// 使用静态HttpClient实例提高性能
    /// </summary>
    public static class HttpService
    {
        // 静态HttpClient实例，用于所有请求
        private static readonly HttpClient _httpClient;

        // 默认超时时间（秒）
        private const int DefaultTimeoutSeconds = 10;

        // 默认JSON序列化选项
        private static readonly JsonSerializerOptions _jsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        // 静态构造函数，初始化HttpClient
        static HttpService()
        {
            // 创建HttpClient实例并配置
            _httpClient = new()
            {
                // 设置默认超时
                Timeout = TimeSpan.FromSeconds(DefaultTimeoutSeconds)
            };

            // 设置默认请求头
            _httpClient.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue("application/json"));
        }

        /// <summary>
        /// 发送POST请求并返回响应
        /// </summary>
        /// <typeparam name="TRequest">请求数据类型</typeparam>
        /// <typeparam name="TResponse">响应数据类型</typeparam>
        /// <param name="url">API地址</param>
        /// <param name="data">请求数据</param>
        /// <param name="timeoutSeconds">超时时间（秒），默认为10秒</param>
        /// <returns>响应数据</returns>
        public static async Task<TResponse?> PostAsync<TRequest, TResponse>(
            string url,
            TRequest data,
            int timeoutSeconds = DefaultTimeoutSeconds)
        {
            // 创建取消令牌，用于超时控制
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));

            try
            {
                // 发送POST请求
                using var response = await _httpClient.PostAsJsonAsync(url, data, cts.Token);

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 读取响应内容
                var content = await response.Content.ReadAsStringAsync(cts.Token);

                // 反序列化结果
                return JsonSerializer.Deserialize<TResponse>(content, _jsonOptions);
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine($"请求超时: {url}");
                throw new TimeoutException($"请求超时: {url}");
            }
            catch (HttpRequestException ex)
            {
                Debug.WriteLine($"HTTP请求错误: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"HTTP请求过程中出错: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 发送GET请求并返回响应
        /// </summary>
        /// <typeparam name="TResponse">响应数据类型</typeparam>
        /// <param name="url">API地址</param>
        /// <param name="timeoutSeconds">超时时间（秒），默认为10秒</param>
        /// <returns>响应数据</returns>
        public static async Task<TResponse?> GetAsync<TResponse>(
            string url,
            int timeoutSeconds = DefaultTimeoutSeconds)
        {
            // 创建取消令牌，用于超时控制
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(timeoutSeconds));

            try
            {
                // 发送GET请求
                using var response = await _httpClient.GetAsync(url, cts.Token);

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 读取响应内容
                var content = await response.Content.ReadAsStringAsync(cts.Token);

                // 反序列化结果
                return JsonSerializer.Deserialize<TResponse>(content, _jsonOptions);
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine($"请求超时: {url}");
                throw new TimeoutException($"请求超时: {url}");
            }
            catch (HttpRequestException ex)
            {
                Debug.WriteLine($"HTTP请求错误: {ex.Message}");
                throw;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"HTTP请求过程中出错: {ex.Message}");
                throw;
            }
        }
    }
}