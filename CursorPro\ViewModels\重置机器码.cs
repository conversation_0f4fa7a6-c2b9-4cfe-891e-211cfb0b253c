using CursorPro.Models;
using MsBox.Avalonia.Enums;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.Versioning;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace CursorPro.ViewModels
{
    /// <summary>
    /// 统一的重置机器码类，支持Windows和Mac平台
    /// </summary>
    public class 重置机器码
    {
        private readonly PlatformPaths _paths;

        public 重置机器码()
        {
            _paths = new PlatformPaths();
        }

        /// <summary>
        /// 重置机器码的主函数（不包含认证数据写入）
        /// </summary>
        public async Task<bool> 执行重置机器码()
        {
            return await 执行重置机器码(null, null, null);
        }

        /// <summary>
        /// 重置机器码的主函数（包含认证数据写入）
        /// </summary>
        public async Task<bool> 执行重置机器码(string? 邮箱, string? 访问令牌, string? 刷新令牌)
        {
            try
            {
                Debug.WriteLine("===== 重置机器码开始 =====");

                // 1. 检查平台和安装状态
                if (!CheckInstallation())
                {
                    Debug.WriteLine("[ERROR] 应用程序未安装或路径不正确，终止重置机器码操作");
                    return false;
                }

                // 2. 获取Cursor路径
                string? cursorPath = GetCursorPath();
                if (string.IsNullOrEmpty(cursorPath))
                {
                    Debug.WriteLine("[ERROR] 无法获取Cursor路径，终止重置机器码操作");
                    await Tools.显示消息框Async("错误", "首次使用请先启动Cursor再获取", ButtonEnum.Ok, Icon.Error);
                    return false;
                }

                Debug.WriteLine($"[INFO] 使用Cursor路径: {cursorPath}");

                // 3. 关闭Cursor进程
                Debug.WriteLine("[INFO] 正在关闭Cursor进程...");
                bool closeResult = await CursorProcessManager.CloseCursorAsync();
                if (!closeResult)
                {
                    Debug.WriteLine("[ERROR] 无法关闭Cursor进程，终止重置机器码操作");
                    await Tools.显示消息框Async("错误", "无法关闭Cursor进程，请手动关闭后再试", ButtonEnum.Ok, Icon.Error);
                    return false;
                }

                // 4. Mac平台不需要等待文件解锁

                // 5. 执行重置操作
                bool resetResult = ExecuteReset(邮箱, 访问令牌, 刷新令牌);
                if (!resetResult)
                {
                    Debug.WriteLine("[ERROR] 重置操作失败");
                    await Tools.显示消息框Async("错误", "重置操作失败", ButtonEnum.Ok, Icon.Error);
                    return false;
                }

                // 6. 启动Cursor应用
                Debug.WriteLine("[INFO] 正在启动Cursor应用...");
                bool startResult = await CursorProcessManager.StartCursorAsync(cursorPath);
                if (!startResult)
                {
                    Debug.WriteLine("[WARN] 启动Cursor应用失败，但重置操作已完成");
                }

                Debug.WriteLine("[INFO] 重置机器码操作完成！");
                Debug.WriteLine("===== 重置机器码结束 =====");

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 重置机器码过程中出错: {ex.Message}");
                Debug.WriteLine($"[ERROR] 堆栈跟踪: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 检查应用程序安装状态
        /// </summary>
        private bool CheckInstallation()
        {
            Debug.WriteLine("[INFO] 开始检查应用程序安装状态...");

            if (OperatingSystem.IsWindows())
            {
                // Windows检查逻辑
                return CheckWindowsInstallation();
            }
            else if (OperatingSystem.IsMacOS())
            {
                // Mac检查逻辑
                return CheckMacInstallation();
            }
            else
            {
                Debug.WriteLine("[ERROR] 不支持的操作系统");
                return false;
            }
        }

        /// <summary>
        /// 获取Cursor应用路径
        /// </summary>
        private string? GetCursorPath()
        {
            return CursorProcessManager.GetCursorPath();
        }

        /// <summary>
        /// 执行重置操作
        /// </summary>
        private bool ExecuteReset(string? 邮箱, string? 访问令牌, string? 刷新令牌)
        {
            try
            {
                Debug.WriteLine("[INFO] 开始执行重置操作...");

                // 1. 跳过ItemTable表清空操作（不再需要）

                // 2. 生成新配置
                if (!GenerateNewConfig(邮箱, 访问令牌, 刷新令牌))
                {
                    Debug.WriteLine("[ERROR] 生成新配置失败");
                    return false;
                }

                // 3. 禁用自动更新
                var 禁用更新实例 = new 禁用更新(_paths);
                if (!禁用更新实例.执行禁用更新())
                {
                    Debug.WriteLine("[WARN] 禁用自动更新失败，但不影响主要功能");
                }

                // 4. 版本检查和修补
                Debug.WriteLine("[INFO] 开始检查Cursor版本...");
                bool versionCheckResult = CheckCursorVersion();
                Debug.WriteLine($"[INFO] 版本检查结果: {versionCheckResult}");

                if (versionCheckResult)
                {
                    Debug.WriteLine("[INFO] 检测到版本 >= 0.45.0，正在修补getMachineId...");
                    bool patchResult = PatchCursorGetMachineId();
                    Debug.WriteLine($"[INFO] 修补getMachineId结果: {patchResult}");
                }
                else
                {
                    Debug.WriteLine("[INFO] 版本低于 0.45.0，跳过修补");
                }

                // 5. 跳过系统UUID更新（已移除，避免权限复杂性）

                // 6. 修改settings.json文件
                bool settingsResult = ModifySettingsJson();
                Debug.WriteLine($"[INFO] 修改settings.json结果: {settingsResult}");

                // 输出完成日志
                LogCompletionDetails();

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 执行重置操作失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 修改settings.json文件
        /// </summary>
        public bool ModifySettingsJson()
        {
            try
            {
                Debug.WriteLine("[INFO] 开始修改settings.json文件...");

                // 确定settings.json文件路径
                string settingsPath = GetSettingsJsonPath();
                Debug.WriteLine($"[INFO] settings.json路径: {settingsPath}");

                // 确保目录存在
                string? settingsDir = Path.GetDirectoryName(settingsPath);
                if (!string.IsNullOrEmpty(settingsDir))
                {
                    Directory.CreateDirectory(settingsDir);
                }

                // 读取现有设置或创建新的
                JsonObject settingsJson;
                if (File.Exists(settingsPath))
                {
                    Debug.WriteLine("[INFO] settings.json文件存在，读取现有内容");
                    string jsonContent = File.ReadAllText(settingsPath);
                    
                    try
                    {
                        settingsJson = JsonSerializer.Deserialize<JsonObject>(jsonContent) ?? new JsonObject();
                    }
                    catch (JsonException)
                    {
                        Debug.WriteLine("[WARN] 解析现有settings.json失败，创建新的");
                        settingsJson = new JsonObject();
                    }
                }
                else
                {
                    Debug.WriteLine("[INFO] settings.json文件不存在，创建新的");
                    settingsJson = new JsonObject();
                }

                // 添加或更新指定的设置
                settingsJson["http.proxy"] = "socks5://2000:2000@43.165.184.49:5555";
                settingsJson["cursor.general.disableHttp2"] = true;
                settingsJson["http.systemCertificates"] = false;
                settingsJson["http.proxySupport"] = "override";
                settingsJson["http.experimental.systemCertificatesV2"] = false;
                settingsJson["http.experimental.systemCertificates"] = false;

                // 写入文件
                string updatedJson = JsonSerializer.Serialize(settingsJson, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(settingsPath, updatedJson);
                Debug.WriteLine("[INFO] settings.json文件写入成功");

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 修改settings.json文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取settings.json文件的路径
        /// </summary>
        private string GetSettingsJsonPath()
        {
            if (OperatingSystem.IsWindows())
            {
                string appData = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                return Path.Combine(appData, "Cursor", "User", "settings.json");
            }
            else if (OperatingSystem.IsMacOS())
            {
                string homeDir = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
                return Path.Combine(homeDir, "Library", "Application Support", "Cursor", "User", "settings.json");
            }
            else
            {
                throw new PlatformNotSupportedException("不支持的操作系统");
            }
        }

        /// <summary>
        /// 删除settings.json中的代理设置键（当弹出购买情况时调用）
        /// </summary>
        public bool DeleteProxySettings()
        {
            try
            {
                Debug.WriteLine("[INFO] 开始删除settings.json中的代理设置...");

                // 确定settings.json文件路径
                string settingsPath = GetSettingsJsonPath();
                Debug.WriteLine($"[INFO] settings.json路径: {settingsPath}");

                // 检查文件是否存在
                if (!File.Exists(settingsPath))
                {
                    Debug.WriteLine("[INFO] settings.json文件不存在，无需删除");
                    return true;
                }

                // 读取现有设置
                string jsonContent = File.ReadAllText(settingsPath);
                JsonObject settingsJson;
                
                try
                {
                    settingsJson = JsonSerializer.Deserialize<JsonObject>(jsonContent) ?? new JsonObject();
                }
                catch (JsonException)
                {
                    Debug.WriteLine("[WARN] 解析settings.json失败，无法删除代理设置");
                    return false;
                }

                // 删除指定的代理设置键
                string[] keysToDelete = {
                    "http.proxy",
                    "cursor.general.disableHttp2",
                    "http.systemCertificates",
                    "http.proxySupport",
                    "http.experimental.systemCertificatesV2",
                    "http.experimental.systemCertificates"
                };

                int deletedCount = 0;
                foreach (string key in keysToDelete)
                {
                    if (settingsJson.Remove(key))
                    {
                        deletedCount++;
                        Debug.WriteLine($"[INFO] 已删除键: {key}");
                    }
                }

                // 写回文件
                string updatedJson = JsonSerializer.Serialize(settingsJson, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(settingsPath, updatedJson);
                Debug.WriteLine($"[INFO] 已删除 {deletedCount} 个代理设置键，settings.json文件已更新");

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 删除代理设置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 输出完成详情日志
        /// </summary>
        private void LogCompletionDetails()
        {
            Debug.WriteLine("[INFO] 机器码重置完成！包括:");
            Debug.WriteLine("[INFO] 1. machineId 文件 (UUID格式)");
            Debug.WriteLine("[INFO] 2. storage.json 中的 telemetry.machineId 等配置");
            Debug.WriteLine("[INFO] 3. 统一数据库事务操作：");
            Debug.WriteLine("[INFO]    - 删除特定键 (__$__targetStorageMarker, cursorai/serverConfig, telemetry.currentSessionDate, aiCodeTrackingLines, telemetry.lastSessionDate)");
            Debug.WriteLine("[INFO]    - 更新 storage.serviceMachineId 键");
            Debug.WriteLine("[INFO]    - 写入认证数据 (如果提供)");
            Debug.WriteLine("[INFO] 4. 版本检查和 main.js 修补 (针对 >= 0.45.0 版本)");
            Debug.WriteLine("[INFO] 5. 修改 settings.json 文件 (设置代理和HTTP选项)");
        }

        /// <summary>
        /// 检查Windows安装状态
        /// </summary>
        [SupportedOSPlatform("windows")]
        private bool CheckWindowsInstallation()
        {
            // Windows版本通常不需要特殊的安装检查
            // 主要检查用户数据目录是否存在
            string? userDataDir = Path.GetDirectoryName(_paths.DbPath);
            if (string.IsNullOrEmpty(userDataDir) || !Directory.Exists(userDataDir))
            {
                Debug.WriteLine($"[ERROR] Cursor用户数据目录不存在: {userDataDir}");
                Debug.WriteLine("[ERROR] 这意味着Cursor从未启动过，请先启动Cursor后再试");
                return false;
            }
            Debug.WriteLine($"[INFO] ✓ Cursor用户数据目录存在: {userDataDir}");
            return true;
        }

        /// <summary>
        /// 检查Mac安装状态
        /// </summary>
        [SupportedOSPlatform("macos")]
        private bool CheckMacInstallation()
        {
            // 检查主要的Cursor应用程序路径
            string cursorAppPath = "/Applications/Cursor.app";
            if (!Directory.Exists(cursorAppPath))
            {
                Debug.WriteLine($"[ERROR] ✗ Cursor应用程序未安装: {cursorAppPath}");
                Debug.WriteLine("[ERROR] 请先安装Cursor应用程序到Applications文件夹");
                return false;
            }
            Debug.WriteLine($"[INFO] ✓ 找到Cursor应用程序: {cursorAppPath}");

            // 检查关键文件是否存在
            var criticalFiles = new[]
            {
                _paths.PackageJsonPath,
                _paths.MainJsPath,
                _paths.ProductJsonPath
            };

            foreach (var filePath in criticalFiles)
            {
                if (!File.Exists(filePath))
                {
                    Debug.WriteLine($"[WARN] ✗ 关键文件不存在: {filePath}");
                    // 不直接返回false，因为某些文件可能在不同版本中位置不同
                }
                else
                {
                    Debug.WriteLine($"[INFO] ✓ 关键文件存在: {filePath}");
                }
            }

            // 检查用户数据目录是否存在（这是最重要的）
            string? userDataDir = Path.GetDirectoryName(_paths.DbPath);
            if (string.IsNullOrEmpty(userDataDir) || !Directory.Exists(userDataDir))
            {
                Debug.WriteLine($"[ERROR] ✗ Cursor用户数据目录不存在: {userDataDir}");
                Debug.WriteLine("[ERROR] 这意味着Cursor从未启动过，请先启动Cursor后再试");
                return false;
            }
            Debug.WriteLine($"[INFO] ✓ Cursor用户数据目录存在: {userDataDir}");

            return true;
        }

        /// <summary>
        /// 生成新配置
        /// </summary>
        private bool GenerateNewConfig(string? 邮箱, string? 访问令牌, string? 刷新令牌)
        {
            try
            {
                Debug.WriteLine("[INFO] 开始生成新配置...");

                // 检查配置文件是否存在
                if (!File.Exists(_paths.DbPath))
                {
                    Debug.WriteLine($"[ERROR] 未找到配置文件: {_paths.DbPath}");
                    Debug.WriteLine("[ERROR] 这可能意味着Cursor从未启动过，请先启动Cursor后再试");
                    return false;
                }
                // Debug.WriteLine($"[INFO] 配置文件存在: {_paths.DbPath}");

                // 批量确保关键文件可写入
                if (!EnsureKeyFilesWritable())
                {
                    Debug.WriteLine($"[ERROR] 无法确保关键文件可写入");
                    return false;
                }

                // 生成新的ID
                Debug.WriteLine("[INFO] 生成新的机器码...");
                var newIds = GenerateNewIds();
                foreach (var pair in newIds)
                {
                    Debug.WriteLine($"[INFO] 生成新ID: {pair.Key} = {pair.Value}");
                }

                // 更新storage.json文件
                if (!UpdateStorageJson(newIds))
                {
                    Debug.WriteLine("[ERROR] 更新storage.json失败");
                    return false;
                }

                // 更新machineId文件
                bool fileResult = UpdateMachineIdFile(newIds["storage.serviceMachineId"]);
                if (fileResult)
                {
                    Debug.WriteLine("[INFO] 已更新machineId文件");
                }
                else
                {
                    Debug.WriteLine("[WARN] 更新machineId文件失败，但不影响主要功能");
                }

                // 统一的数据库操作：删除特定键、更新serviceMachineId、写入认证数据、设置登录类型
                bool dbResult = UpdateDatabaseInTransaction(newIds["storage.serviceMachineId"], 邮箱, 访问令牌, 刷新令牌);
                if (dbResult)
                {
                    Debug.WriteLine("[INFO] 已完成统一数据库操作（删除特定键、更新serviceMachineId、写入认证数据、设置登录类型）");
                }
                else
                {
                    Debug.WriteLine("[WARN] 数据库操作失败，但不影响主要功能");
                }

                // 跳过系统UUID更新（不再需要，避免权限复杂性）

                Debug.WriteLine("[INFO] 新配置生成完成");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 生成新配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成新的ID集合
        /// </summary>
        private Dictionary<string, string> GenerateNewIds()
        {
            var newIds = new Dictionary<string, string>();

            // 生成基础ID
            string machineId = Guid.NewGuid().ToString("N");
            string macMachineId = Guid.NewGuid().ToString("N");
            string deviceId = Guid.NewGuid().ToString("N");
            string sqmId = Guid.NewGuid().ToString("N");
            string uuidMachineId = Guid.NewGuid().ToString();

            // 添加到字典
            newIds["telemetry.machineId"] = machineId;
            newIds["telemetry.macMachineId"] = macMachineId;
            newIds["telemetry.devDeviceId"] = deviceId;
            newIds["telemetry.sqmId"] = sqmId;
            newIds["storage.serviceMachineId"] = uuidMachineId;

            return newIds;
        }

        /// <summary>
        /// 更新storage.json文件
        /// </summary>
        private bool UpdateStorageJson(Dictionary<string, string> newIds)
        {
            try
            {
                Debug.WriteLine("[INFO] 正在更新storage.json文件...");
                string jsonContent = File.ReadAllText(_paths.DbPath);
                Debug.WriteLine($"[INFO] 配置文件内容长度: {jsonContent.Length}字节");

                var configJson = System.Text.Json.JsonDocument.Parse(jsonContent).RootElement;
                Debug.WriteLine("[INFO] JSON解析成功");

                // 更新JSON文件
                var jsonObject = new Dictionary<string, object>();
                using (System.Text.Json.JsonDocument doc = System.Text.Json.JsonDocument.Parse(jsonContent))
                {
                    foreach (System.Text.Json.JsonProperty property in doc.RootElement.EnumerateObject())
                    {
                        jsonObject[property.Name] = property.Value.Clone();
                    }
                }
                Debug.WriteLine($"[INFO] 读取到原始JSON对象，包含{jsonObject.Count}个属性");

                foreach (var pair in newIds)
                {
                    jsonObject[pair.Key] = pair.Value;
                    Debug.WriteLine($"[INFO] 更新属性: {pair.Key}");
                }

                string updatedJson = System.Text.Json.JsonSerializer.Serialize(jsonObject, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
                Debug.WriteLine($"[INFO] 序列化后的JSON长度: {updatedJson.Length}字节");

                // 跳过重复的可写入检查（已在GenerateNewConfig开始时检查过）

                File.WriteAllText(_paths.DbPath, updatedJson);
                Debug.WriteLine("[INFO] storage.json文件写入成功");

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 更新storage.json文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 更新machineId文件
        /// </summary>
        private bool UpdateMachineIdFile(string uuidMachineId)
        {
            try
            {
                Debug.WriteLine($"[INFO] 正在更新machineId文件: {_paths.MachineIdPath}");

                // 确保目录存在
                string? parentDir = Path.GetDirectoryName(_paths.MachineIdPath);
                if (!string.IsNullOrEmpty(parentDir))
                {
                    Directory.CreateDirectory(parentDir);
                }

                // 跳过machineId文件权限检查（已在批量检查中处理）

                // 写入新的UUID格式的machineId
                File.WriteAllText(_paths.MachineIdPath, uuidMachineId);
                Debug.WriteLine("[INFO] 成功写入machineId文件");

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 更新machineId文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 统一的数据库操作：删除特定键、更新serviceMachineId、写入认证数据、设置登录类型
        /// </summary>
        private bool UpdateDatabaseInTransaction(string uuidMachineId, string? 邮箱 = null, string? 访问令牌 = null, string? 刷新令牌 = null)
        {
            Debug.WriteLine("[INFO] 正在执行统一数据库操作（删除特定键、更新serviceMachineId、写入认证数据、设置登录类型）...");

            try
            {
                // 检查数据库文件是否存在
                if (!File.Exists(_paths.SqlitePath))
                {
                    Debug.WriteLine($"[WARN] 数据库文件不存在: {_paths.SqlitePath}");
                    return false;
                }

                // 要删除的键列表
                var keysToDelete = new[]
                {
                    "__$__targetStorageMarker",
                    "cursorai/serverConfig",
                    "telemetry.currentSessionDate",
                    "aiCodeTrackingLines",
                    "telemetry.lastSessionDate"
                };

                using (var conn = new Microsoft.Data.Sqlite.SqliteConnection($"Data Source={_paths.SqlitePath}"))
                {
                    conn.Open();

                    // 开始事务以确保数据一致性
                    using (var transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            int totalDeleted = 0;
                            int totalUpdated = 0;

                            // 1. 删除特定键
                            foreach (var key in keysToDelete)
                            {
                                using (var cmd = new Microsoft.Data.Sqlite.SqliteCommand("DELETE FROM ItemTable WHERE key = @key", conn))
                                {
                                    cmd.Transaction = transaction; // 关联事务
                                    cmd.Parameters.AddWithValue("@key", key);
                                    int deletedRows = cmd.ExecuteNonQuery();
                                    totalDeleted += deletedRows;

                                    if (deletedRows > 0)
                                    {
                                        Debug.WriteLine($"[INFO] 已删除键: {key}");
                                    }
                                    else
                                    {
                                        Debug.WriteLine($"[INFO] 键不存在，跳过: {key}");
                                    }
                                }
                            }

                            // 2. 更新storage.serviceMachineId键
                            using (var cmd = new Microsoft.Data.Sqlite.SqliteCommand(
                                "INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", conn))
                            {
                                cmd.Transaction = transaction; // 关联事务
                                cmd.Parameters.AddWithValue("@key", "storage.serviceMachineId");
                                cmd.Parameters.AddWithValue("@value", uuidMachineId);
                                int affectedRows = cmd.ExecuteNonQuery();
                                totalUpdated += affectedRows;
                                Debug.WriteLine($"[INFO] 已更新storage.serviceMachineId键");
                            }

                            // 3. 写入认证数据（如果提供）
                            if (!string.IsNullOrEmpty(邮箱) && !string.IsNullOrEmpty(访问令牌) && !string.IsNullOrEmpty(刷新令牌))
                            {
                                // 写入邮箱
                                using (var cmd = new Microsoft.Data.Sqlite.SqliteCommand(
                                    "INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", conn))
                                {
                                    cmd.Transaction = transaction; // 关联事务
                                    cmd.Parameters.AddWithValue("@key", "cursorAuth/cachedEmail");
                                    cmd.Parameters.AddWithValue("@value", 邮箱);
                                    cmd.ExecuteNonQuery();
                                    totalUpdated++;
                                    Debug.WriteLine($"[INFO] 已写入用户邮箱: {邮箱}");
                                }

                                // 写入访问令牌
                                using (var cmd = new Microsoft.Data.Sqlite.SqliteCommand(
                                    "INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", conn))
                                {
                                    cmd.Transaction = transaction; // 关联事务
                                    cmd.Parameters.AddWithValue("@key", "cursorAuth/accessToken");
                                    cmd.Parameters.AddWithValue("@value", 访问令牌);
                                    cmd.ExecuteNonQuery();
                                    totalUpdated++;
                                    Debug.WriteLine("[INFO] 已写入访问令牌");
                                }

                                // 写入刷新令牌
                                using (var cmd = new Microsoft.Data.Sqlite.SqliteCommand(
                                    "INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", conn))
                                {
                                    cmd.Transaction = transaction; // 关联事务
                                    cmd.Parameters.AddWithValue("@key", "cursorAuth/refreshToken");
                                    cmd.Parameters.AddWithValue("@value", 刷新令牌);
                                    cmd.ExecuteNonQuery();
                                    totalUpdated++;
                                    Debug.WriteLine("[INFO] 已写入刷新令牌");
                                }
                            }

                            // 添加写入cachedSignUpType，无论是否提供了认证数据都写入
                            using (var cmd = new Microsoft.Data.Sqlite.SqliteCommand(
                                "INSERT OR REPLACE INTO ItemTable (key, value) VALUES (@key, @value)", conn))
                            {
                                cmd.Transaction = transaction; // 关联事务
                                cmd.Parameters.AddWithValue("@key", "cursorAuth/cachedSignUpType");
                                cmd.Parameters.AddWithValue("@value", "github");
                                cmd.ExecuteNonQuery();
                                totalUpdated++;
                                Debug.WriteLine("[INFO] 已写入登录类型: github");
                            }

                            // 提交事务
                            transaction.Commit();
                            Debug.WriteLine($"[INFO] 数据库事务已提交，删除了 {totalDeleted} 个键，更新了 {totalUpdated} 个键");

                            return true;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            Debug.WriteLine($"[ERROR] 数据库事务失败，已回滚: {ex.Message}");
                            return false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 数据库操作失败: {ex.Message}");
                return false;
            }
            finally
            {
                // 强制清理SQLite连接池
                Microsoft.Data.Sqlite.SqliteConnection.ClearAllPools();
                Debug.WriteLine("[INFO] 已清理SQLite连接池");
            }
        }

        /// <summary>
        /// 批量确保关键文件可写入
        /// </summary>
        private bool EnsureKeyFilesWritable()
        {
            Debug.WriteLine("[INFO] 开始批量检查关键文件权限...");

            var filesToCheck = new[]
            {
                new { Path = _paths.DbPath, Name = "storage.json", Optional = false },
                new { Path = _paths.SqlitePath, Name = "state.vscdb", Optional = false },
                new { Path = _paths.MachineIdPath, Name = "machineId", Optional = true },
                new { Path = _paths.MainJsPath, Name = "main.js", Optional = true }
            };

            bool criticalSuccess = true;  // 只关注必需文件
            int checkedCount = 0;
            int skippedCount = 0;
            int failedOptionalCount = 0;

            foreach (var file in filesToCheck)
            {
                // 对于可选文件，如果不存在则跳过
                if (file.Optional == true && !File.Exists(file.Path))
                {
                    Debug.WriteLine($"[INFO] 可选文件不存在，跳过: {file.Name}");
                    skippedCount++;
                    continue;
                }

                if (!FileHelper.EnsureFileWritable(file.Path))
                {
                    if (file.Optional == true)
                    {
                        Debug.WriteLine($"[WARN] 可选文件权限设置失败，将跳过相关功能: {file.Name} ({file.Path})");
                        failedOptionalCount++;
                    }
                    else
                    {
                        Debug.WriteLine($"[ERROR] 必需文件权限设置失败: {file.Name} ({file.Path})");
                        criticalSuccess = false;
                    }
                }
                else
                {
                    checkedCount++;
                    Debug.WriteLine($"[INFO] 文件权限检查成功: {file.Name}");
                }
            }

            Debug.WriteLine($"[INFO] 文件权限检查完成 - 成功: {checkedCount}个, 跳过: {skippedCount}个, 可选文件失败: {failedOptionalCount}个");
            Debug.WriteLine($"[INFO] 关键文件权限检查结果: {(criticalSuccess ? "成功" : "失败")}");

            return criticalSuccess;  // 只要必需文件成功就返回true
        }
        /// <summary>
        /// 检查Cursor版本
        /// </summary>
        private bool CheckCursorVersion()
        {
            try
            {
                Debug.WriteLine($"[INFO] 正在读取package.json: {_paths.PackageJsonPath}");

                if (!File.Exists(_paths.PackageJsonPath))
                {
                    Debug.WriteLine($"[ERROR] package.json文件不存在: {_paths.PackageJsonPath}");
                    return false;
                }

                string jsonContent = File.ReadAllText(_paths.PackageJsonPath);
                using (var doc = System.Text.Json.JsonDocument.Parse(jsonContent))
                {
                    if (doc.RootElement.TryGetProperty("version", out var versionElement))
                    {
                        string? version = versionElement.GetString();
                        Debug.WriteLine($"[INFO] 检测到Cursor版本: {version}");

                        if (string.IsNullOrEmpty(version))
                        {
                            Debug.WriteLine("[ERROR] 版本字符串为空");
                            return false;
                        }

                        // 检查版本格式
                        if (!Regex.IsMatch(version, @"^\d+\.\d+\.\d+$"))
                        {
                            Debug.WriteLine($"[ERROR] 无效的版本格式: {version}");
                            return false;
                        }

                        // 比较版本是否 >= 0.45.0
                        var versionParts = version.Split('.').Select(int.Parse).ToArray();
                        var targetVersion = new[] { 0, 45, 0 };

                        for (int i = 0; i < 3; i++)
                        {
                            if (versionParts[i] > targetVersion[i])
                            {
                                Debug.WriteLine($"[INFO] 版本 {version} >= 0.45.0，需要修补");
                                return true;
                            }
                            if (versionParts[i] < targetVersion[i])
                            {
                                Debug.WriteLine($"[INFO] 版本 {version} < 0.45.0，跳过修补");
                                return false;
                            }
                        }

                        // 如果所有比较都相等，则版本相等
                        return true;
                    }
                    else
                    {
                        Debug.WriteLine("[ERROR] 没有找到version字段");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 检查版本失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 修补Cursor的getMachineId函数
        /// </summary>
        private bool PatchCursorGetMachineId()
        {
            try
            {
                Debug.WriteLine("[INFO] 开始修补getMachineId...");

                // 检查文件权限
                if (!File.Exists(_paths.MainJsPath))
                {
                    Debug.WriteLine($"[ERROR] 文件不存在: {_paths.MainJsPath}");
                    return false;
                }

                // 修改文件
                if (!ModifyMainJs())
                {
                    return false;
                }

                Debug.WriteLine("[INFO] 修补完成");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 修补失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 修改main.js文件内容
        /// </summary>
        private bool ModifyMainJs()
        {
            try
            {
                Debug.WriteLine("[INFO] 开始修改main.js文件内容...");

                // 读取main.js内容
                string content = File.ReadAllText(_paths.MainJsPath);

                // 执行替换
                bool modified = false;

                // 修改getMachineId函数
                string pattern1 = @"async getMachineId\(\)\{return [^??]+\?\?([^}]+)\}";
                string replacement1 = "async getMachineId(){return $1}";

                // 修改getMacMachineId函数
                string pattern2 = @"async getMacMachineId\(\)\{return [^??]+\?\?([^}]+)\}";
                string replacement2 = "async getMacMachineId(){return $1}";

                var newContent = Regex.Replace(content, pattern1, replacement1);
                if (newContent != content)
                {
                    content = newContent;
                    modified = true;
                    Debug.WriteLine("[INFO] 成功修改getMachineId函数");
                }

                newContent = Regex.Replace(content, pattern2, replacement2);
                if (newContent != content)
                {
                    content = newContent;
                    modified = true;
                    Debug.WriteLine("[INFO] 成功修改getMacMachineId函数");
                }

                if (modified)
                {
                    // 直接写入原文件（优化性能，减少I/O操作）
                    File.WriteAllText(_paths.MainJsPath, content);

                    Debug.WriteLine("[INFO] main.js文件修改完成");
                    return true;
                }
                else
                {
                    Debug.WriteLine("[INFO] main.js文件无需修改");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 修改main.js文件失败: {ex.Message}");
                return false;
            }
        }




    }

    /// <summary>
    /// 平台相关路径管理类
    /// </summary>
    public class PlatformPaths
    {
        public string DbPath { get; private set; } = string.Empty;
        public string SqlitePath { get; private set; } = string.Empty;
        public string MachineIdPath { get; private set; } = string.Empty;
        public string WorkbenchPath { get; private set; } = string.Empty;
        public string PackageJsonPath { get; private set; } = string.Empty;
        public string MainJsPath { get; private set; } = string.Empty;
        public string UpdaterPath { get; private set; } = string.Empty;
        public string UpdateYmlPath { get; private set; } = string.Empty;
        public string ProductJsonPath { get; private set; } = string.Empty;

        public PlatformPaths()
        {
            InitializePaths();
        }

        private void InitializePaths()
        {
            if (OperatingSystem.IsWindows())
            {
                InitializeWindowsPaths();
            }
            else if (OperatingSystem.IsMacOS())
            {
                InitializeMacPaths();
            }
            else
            {
                Debug.WriteLine("[ERROR] 不支持的操作系统");
                // 设置空路径，避免后续操作
                SetEmptyPaths();
            }
        }

        /// <summary>
        /// 设置空路径（用于不支持的平台或路径获取失败）
        /// </summary>
        private void SetEmptyPaths()
        {
            WorkbenchPath = "";
            PackageJsonPath = "";
            MainJsPath = "";
            ProductJsonPath = "";
            UpdateYmlPath = "";
            UpdaterPath = "";
            DbPath = "";
            SqlitePath = "";
            MachineIdPath = "";
        }

        [SupportedOSPlatform("windows")]
        private void InitializeWindowsPaths()
        {
            // 查找Cursor用户数据路径（支持多用户环境）
            string? cursorUserDataPath = FindCursorUserDataPath();

            if (string.IsNullOrEmpty(cursorUserDataPath))
            {
                Debug.WriteLine("[ERROR] 未找到任何用户的Cursor数据文件夹");
                // 使用当前用户路径作为默认值，即使不存在
                string currentUserRoaming = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                cursorUserDataPath = Path.Combine(currentUserRoaming, "Cursor");
                Debug.WriteLine($"[WARN] 使用当前用户默认路径: {cursorUserDataPath}");
            }

            DbPath = Path.Combine(cursorUserDataPath, "User", "globalStorage", "storage.json");
            SqlitePath = Path.Combine(cursorUserDataPath, "User", "globalStorage", "state.vscdb");
            MachineIdPath = Path.Combine(cursorUserDataPath, "machineId");

            // 使用缓存的Cursor安装路径（性能优化）
            string? cursorInstallPath = CursorProcessManager.GetCachedInstallPath();

            if (string.IsNullOrEmpty(cursorInstallPath))
            {
                Debug.WriteLine($"[ERROR] 无法获取Cursor安装路径，无法初始化相关文件路径");
                // 只清空安装相关路径，保留用户数据路径
                WorkbenchPath = "";
                PackageJsonPath = "";
                MainJsPath = "";
                ProductJsonPath = "";
                UpdateYmlPath = "";
                return;
            }

            Debug.WriteLine($"[INFO] 使用缓存的Cursor安装目录: {cursorInstallPath}");

            WorkbenchPath = Path.Combine(cursorInstallPath, "resources", "app", "out", "vs", "workbench", "workbench.desktop.main.js");
            PackageJsonPath = Path.Combine(cursorInstallPath, "resources", "app", "package.json");
            MainJsPath = Path.Combine(cursorInstallPath, "resources", "app", "out", "main.js");
            ProductJsonPath = Path.Combine(cursorInstallPath, "resources", "app", "product.json");

            UpdaterPath = Path.Combine(cursorUserDataPath, "cursor-updater");
            UpdateYmlPath = Path.Combine(cursorInstallPath, "resources", "app-update.yml");
        }

        /// <summary>
        /// 查找Cursor用户数据路径（仅检查当前用户的Roaming文件夹）
        /// </summary>
        [SupportedOSPlatform("windows")]
        private static string? FindCursorUserDataPath()
        {
            Debug.WriteLine("[INFO] 开始查找Cursor用户数据路径...");

            // 检查当前登录用户的Roaming文件夹
            string currentUserRoaming = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            string currentUserCursorPath = Path.Combine(currentUserRoaming, "Cursor");

            Debug.WriteLine($"[INFO] 检查当前用户Roaming路径: {currentUserCursorPath}");
            if (Directory.Exists(currentUserCursorPath))
            {
                Debug.WriteLine($"[SUCCESS] 找到当前用户的Cursor数据: {currentUserCursorPath}");
                return currentUserCursorPath;
            }

            Debug.WriteLine("[INFO] 当前用户Roaming文件夹中未找到Cursor数据，请确保Cursor已安装并运行过");
            return null;
        }



        [SupportedOSPlatform("macos")]
        private void InitializeMacPaths()
        {
            string userHome = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            string appSupportPath = Path.Combine(userHome, "Library", "Application Support", "Cursor");
            string appSupportRootPath = Path.Combine(userHome, "Library", "Application Support");

            DbPath = Path.Combine(appSupportPath, "User", "globalStorage", "storage.json");
            SqlitePath = Path.Combine(appSupportPath, "User", "globalStorage", "state.vscdb");
            MachineIdPath = Path.Combine(appSupportPath, "machineId");

            WorkbenchPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app", "out", "vs", "workbench", "workbench.desktop.main.js");
            PackageJsonPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app", "package.json");
            MainJsPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app", "out", "main.js");
            ProductJsonPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app", "product.json");

            UpdaterPath = Path.Combine(appSupportRootPath, "cursor-updater");
            UpdateYmlPath = Path.Combine("/Applications", "Cursor.app", "Contents", "Resources", "app-update.yml");
        }
    }
}
