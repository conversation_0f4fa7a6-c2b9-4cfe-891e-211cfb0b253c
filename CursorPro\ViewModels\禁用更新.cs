using CursorPro.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;

namespace CursorPro.ViewModels
{
    /// <summary>
    /// 禁用Cursor自动更新功能的工具类
    /// 负责删除更新程序目录和移除更新配置
    /// </summary>
    public class 禁用更新
    {
        private readonly PlatformPaths _paths;

        /// <summary>
        /// 初始化禁用更新实例
        /// </summary>
        public 禁用更新()
        {
            _paths = new PlatformPaths();
        }

        /// <summary>
        /// 初始化禁用更新实例（使用指定的路径配置）
        /// </summary>
        /// <param name="paths">平台路径配置</param>
        public 禁用更新(PlatformPaths paths)
        {
            _paths = paths ?? throw new ArgumentNullException(nameof(paths));
        }

        /// <summary>
        /// 执行禁用自动更新操作
        /// </summary>
        /// <returns>操作是否成功</returns>
        public bool 执行禁用更新()
        {
            Debug.WriteLine("[INFO] ===== 禁用自动更新开始 =====");

            try
            {
                // 1. 处理更新程序目录
                Debug.WriteLine("[INFO] 处理更新程序目录...");
                bool removeUpdaterResult = 删除更新程序目录();
                Debug.WriteLine($"[INFO] 处理更新程序目录结果: {removeUpdaterResult}");

                // 2. 移除更新URL
                Debug.WriteLine("[INFO] 移除更新URL...");
                bool removeUrlResult = 移除更新URL();
                Debug.WriteLine($"[INFO] 移除更新URL结果: {removeUrlResult}");

                Debug.WriteLine("[INFO] ===== 禁用自动更新结束 =====");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 禁用自动更新过程中出错: {ex.Message}");
                Debug.WriteLine($"[ERROR] 堆栈跟踪: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 删除更新程序目录
        /// </summary>
        /// <returns>操作是否成功</returns>
        public bool 删除更新程序目录()
        {
            try
            {
                Debug.WriteLine($"[INFO] 检查更新程序目录: {_paths.UpdaterPath}");

                if (Directory.Exists(_paths.UpdaterPath))
                {
                    try
                    {
                        Directory.Delete(_paths.UpdaterPath, true);
                        Debug.WriteLine("[INFO] 更新程序目录已删除");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[WARN] 删除更新程序目录失败: {ex.Message}");
                    }
                }
                else
                {
                    Debug.WriteLine("[INFO] 更新程序目录不存在，无需删除");
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 处理更新程序目录失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除更新URL配置
        /// </summary>
        /// <returns>操作是否成功</returns>
        public bool 移除更新URL()
        {
            try
            {
                Debug.WriteLine($"[INFO] 检查产品配置文件: {_paths.ProductJsonPath}");

                if (File.Exists(_paths.ProductJsonPath))
                {
                    try
                    {
                        // 读取文件内容
                        string content = File.ReadAllText(_paths.ProductJsonPath);
                        Debug.WriteLine($"[INFO] 已读取产品配置文件，大小: {content.Length}字节");

                        // 解析JSON并移除更新URL
                        using (var doc = System.Text.Json.JsonDocument.Parse(content))
                        {
                            var jsonObject = new Dictionary<string, object>();
                            foreach (var property in doc.RootElement.EnumerateObject())
                            {
                                // 跳过更新相关的属性
                                if (property.Name != "updateUrl" && property.Name != "updateChannel")
                                {
                                    jsonObject[property.Name] = property.Value.Clone();
                                }
                                else
                                {
                                    Debug.WriteLine($"[INFO] 移除属性: {property.Name}");
                                }
                            }

                            // 序列化并写回文件
                            string updatedContent = System.Text.Json.JsonSerializer.Serialize(jsonObject,
                                new System.Text.Json.JsonSerializerOptions { WriteIndented = true });

                            // 确保文件可写入
                            FileHelper.EnsureFileWritable(_paths.ProductJsonPath);
                            File.WriteAllText(_paths.ProductJsonPath, updatedContent);
                            Debug.WriteLine("[INFO] 产品配置文件已更新");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[WARN] 处理产品配置文件失败: {ex.Message}");
                    }
                }
                else
                {
                    Debug.WriteLine("[INFO] 产品配置文件不存在，跳过");
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 移除更新URL失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否存在更新相关文件
        /// </summary>
        /// <returns>更新文件检查结果</returns>
        public (bool HasUpdaterDirectory, bool HasProductJson, bool HasUpdateConfig) 检查更新文件状态()
        {
            try
            {
                bool hasUpdaterDirectory = Directory.Exists(_paths.UpdaterPath);
                bool hasProductJson = File.Exists(_paths.ProductJsonPath);
                bool hasUpdateConfig = false;

                // 检查产品配置文件中是否包含更新配置
                if (hasProductJson)
                {
                    try
                    {
                        string content = File.ReadAllText(_paths.ProductJsonPath);
                        using (var doc = System.Text.Json.JsonDocument.Parse(content))
                        {
                            hasUpdateConfig = doc.RootElement.TryGetProperty("updateUrl", out _) ||
                                            doc.RootElement.TryGetProperty("updateChannel", out _);
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[WARN] 检查产品配置文件失败: {ex.Message}");
                    }
                }

                Debug.WriteLine($"[INFO] 更新文件状态检查结果:");
                Debug.WriteLine($"[INFO] - 更新程序目录存在: {hasUpdaterDirectory}");
                Debug.WriteLine($"[INFO] - 产品配置文件存在: {hasProductJson}");
                Debug.WriteLine($"[INFO] - 包含更新配置: {hasUpdateConfig}");

                return (hasUpdaterDirectory, hasProductJson, hasUpdateConfig);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 检查更新文件状态失败: {ex.Message}");
                return (false, false, false);
            }
        }

        /// <summary>
        /// 获取更新程序目录路径
        /// </summary>
        /// <returns>更新程序目录路径</returns>
        public string 获取更新程序目录路径()
        {
            return _paths.UpdaterPath;
        }

        /// <summary>
        /// 获取产品配置文件路径
        /// </summary>
        /// <returns>产品配置文件路径</returns>
        public string 获取产品配置文件路径()
        {
            return _paths.ProductJsonPath;
        }

        /// <summary>
        /// 验证禁用更新操作是否成功
        /// </summary>
        /// <returns>验证结果</returns>
        public bool 验证禁用更新结果()
        {
            try
            {
                var (hasUpdaterDirectory, hasProductJson, hasUpdateConfig) = 检查更新文件状态();

                // 如果更新程序目录不存在且产品配置文件中没有更新配置，则认为禁用成功
                bool isDisabled = !hasUpdaterDirectory && !hasUpdateConfig;

                Debug.WriteLine($"[INFO] 禁用更新验证结果: {(isDisabled ? "成功" : "失败")}");

                return isDisabled;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 验证禁用更新结果失败: {ex.Message}");
                return false;
            }
        }
    }
}
