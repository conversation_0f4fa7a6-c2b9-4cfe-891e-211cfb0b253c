{"version": "2.0.0", "tasks": [{"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/CursorPro/CursorPro.csproj"], "problemMatcher": "$msCompile"}, {"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/CursorPro/CursorPro.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile", "dependsOn": ["clean"]}, {"label": "build-with-clean", "dependsOn": ["clean", "build"], "dependsOrder": "sequence"}, {"label": "run", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/CursorPro/CursorPro.csproj"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/CursorPro/CursorPro.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/CursorPro/CursorPro.csproj"], "problemMatcher": "$msCompile"}]}