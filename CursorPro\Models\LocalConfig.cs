using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text.Json.Nodes;

namespace CursorPro.Models
{
    /// <summary>
    /// 处理本地配置文件的读写操作
    /// 使用方法:
    /// LocalConfig.读取("键名");
    /// LocalConfig.写入("键名", "值");
    /// 首次使用会自动创建config.json文件
    /// </summary>
    public static class LocalConfig
    {
        // 配置文件的存储路径
        private static readonly string 配置目录 = GetConfigDirectory();

        public static readonly string 默认配置文件 = Path.Combine(配置目录, "system_cache.dat");//使用.dat扩展名更不引人注目

        private static JsonNode? _jsonObject;

        // 获取适合当前操作系统的配置目录
        private static string GetConfigDirectory()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                // Windows: 使用 AppData 目录
                return Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "NVIDIA_NV" //伪装的名字
                );
            }

            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                // macOS: 使用 ~/Library/Application Support 目录
                string homeDir = Environment.GetEnvironmentVariable("HOME") ?? "/Users/" + Environment.UserName;
                return Path.Combine(homeDir, "Library", "Application Support", "NVIDIA_NV");
            }
            else
            {
                // 其他平台: 使用用户目录下的隐藏文件夹
                string homeDir = Environment.GetEnvironmentVariable("HOME") ?? Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
                return Path.Combine(homeDir, ".NVIDIA_NV");
            }
        }

        static LocalConfig()
        {
            初始化();
        }

        // 初始化配置目录和文件
        private static void 初始化()
        {
            try
            {
                if (!Directory.Exists(配置目录))
                {
                    Directory.CreateDirectory(配置目录);
                }

                if (File.Exists(默认配置文件))
                {
                    string json = File.ReadAllText(默认配置文件);
                    if (string.IsNullOrWhiteSpace(json))
                    {
                        json = "{}";
                        File.WriteAllText(默认配置文件, json);
                    }
                    _jsonObject = JsonNode.Parse(json);
                }
                else
                {
                    _jsonObject = new JsonObject();
                    File.WriteAllText(默认配置文件, "{}");
                }
            }
            catch (Exception ex)
            {
                // 记录错误信息
                Debug.WriteLine($"初始化配置时出错: {ex.Message}");
                // 确保_jsonObject不为null
                _jsonObject = new JsonObject();
            }
        }

        // 读取配置项
        public static string? 读取(string 键名)
        {
            try
            {
                var jsonValue = _jsonObject?[键名];
                string? 结果 = null;

                // 根据不同类型处理返回值
                if (jsonValue != null)
                {
                    if (jsonValue is JsonValue value)
                    {
                        // 获取实际值而不是JSON表示
                        结果 = value.GetValue<object>()?.ToString();
                    }
                    else
                    {
                        // 复杂对象或数组，保留JSON字符串形式
                        结果 = jsonValue.ToString();
                    }
                }

                Debug.WriteLine($"读取配置: {键名} = {结果}");
                return 结果;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"读取配置时出错: {ex.Message}");
                return null;
            }
        }

        // 保存配置项
        public static void 写入(string 键名, object? 值)
        {
            try
            {
                if (_jsonObject == null) _jsonObject = new JsonObject();
                _jsonObject[键名] = JsonValue.Create(值);

                Debug.WriteLine($"写入配置: {键名} = {值}");

                // 直接写入文件
                File.WriteAllText(默认配置文件, _jsonObject.ToJsonString());
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存配置时出错: {ex.Message}");
            }
        }
    }
}