using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using System;
using System.Diagnostics;

namespace CursorPro.ViewModels
{
    public class Mutex
    {
        private static System.Threading.Mutex? _mutex;
        private static readonly string _mutexName = "CursorPro_SingleInstance";

        public static bool Initialize()
        {
            try
            {
                _mutex = new System.Threading.Mutex(true, _mutexName, out bool createdNew);

                if (!createdNew)
                {
                    // 不显示消息框，只激活已存在的窗口
                    ActivateExistingWindow();

                    if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktopLifetime)
                    {
                        desktopLifetime.Shutdown();
                    }
                    return false;
                }

                if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktopApp)
                {
                    desktopApp.Exit += (s, e) =>
                    {
                        _mutex?.ReleaseMutex();
                        _mutex?.Dispose();
                    };
                }

                return true;
            }
            catch (Exception ex)
            {
                // 不显示错误消息，只记录日志
                Debug.WriteLine($"初始化程序时发生错误：{ex.Message}");
                return false;
            }
        }

        private static Window? GetMainWindow()
        {
            if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                return desktop.MainWindow;
            }
            return null;
        }

        private static void ActivateExistingWindow()
        {
            try
            {
                Process current = Process.GetCurrentProcess();
                foreach (Process process in Process.GetProcessesByName(current.ProcessName))
                {
                    if (process.Id != current.Id)
                    {
                        if (OperatingSystem.IsWindows())
                        {
                            WindowsNativeMethods.SetForegroundWindow(process.MainWindowHandle);
                        }
                        else if (OperatingSystem.IsMacOS())
                        {
                            // 在macOS上使用AppleScript激活窗口
                            ActivateMacOSWindow(current.ProcessName);
                        }

                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                // 不显示错误消息，只记录日志
                Debug.WriteLine($"激活已存在的窗口时发生错误：{ex.Message}");
            }
        }

        private static void ActivateMacOSWindow(string processName)
        {
            try
            {
                // 应用程序名称通常不包含扩展名
                string appName = processName;
                if (appName.EndsWith(".exe"))
                {
                    appName = appName.Substring(0, appName.Length - 4);
                }

                // 创建AppleScript脚本激活应用
                string script = $"tell application \"System Events\"\n" +
                                $"    set frontmost of every process whose name contains \"{appName}\" to true\n" +
                                "end tell";

                // 执行AppleScript
                var startInfo = new ProcessStartInfo
                {
                    FileName = "/usr/bin/osascript",
                    Arguments = $"-e \"{script}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    process?.WaitForExit(1000); // 等待最多1秒
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"激活Mac窗口时出错: {ex.Message}");
            }
        }


    }

    internal static class WindowsNativeMethods
    {
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        internal static extern bool SetForegroundWindow(IntPtr hWnd);
    }
}