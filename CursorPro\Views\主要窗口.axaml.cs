using Avalonia.Controls;
using System;
using System.Diagnostics;

namespace CursorPro.Views
{
    public partial class 主要窗口 : Window
    {
        public 主要窗口()
        {
            InitializeComponent();

            // 添加窗口关闭事件处理
            Closed += 主要窗口_Closed;
        }



        private void 主要窗口_Closed(object? sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("主要窗口正在关闭，释放资源...");

                // 释放ViewModel资源
                if (DataContext is IDisposable disposableViewModel)
                {
                    disposableViewModel.Dispose();
                    Debug.WriteLine("主要窗口ViewModel资源已释放");
                }

                DataContext = null;
                Debug.WriteLine("主要窗口关闭完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"主要窗口关闭时出错: {ex.Message}");
            }
        }
    }
}