using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.Versioning;

namespace CursorPro.Models
{
    /// <summary>
    /// 文件操作辅助工具类
    /// 提供跨平台的文件权限管理和状态检查功能
    /// </summary>
    public static class FileHelper
    {
        /// <summary>
        /// 移除文件的只读属性（跨平台）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>操作是否成功</returns>
        public static bool RemoveReadOnlyAttribute(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    Debug.WriteLine("[ERROR] 文件路径为空");
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Debug.WriteLine($"[INFO] 文件不存在，无需移除只读属性: {filePath}");
                    return true; // 文件不存在时返回true，因为目标已达成
                }

                if (OperatingSystem.IsWindows())
                {
                    return RemoveReadOnlyAttributeWindows(filePath);
                }
                else if (OperatingSystem.IsMacOS())
                {
                    return RemoveReadOnlyAttributeMac(filePath);
                }
                else if (OperatingSystem.IsLinux())
                {
                    return RemoveReadOnlyAttributeLinux(filePath);
                }
                else
                {
                    Debug.WriteLine("[ERROR] 不支持的操作系统");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 移除只读属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查文件是否可写入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件是否可写入</returns>
        public static bool IsFileWritable(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    Debug.WriteLine("[ERROR] 文件路径为空");
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Debug.WriteLine($"[INFO] 文件不存在: {filePath}");
                    return false;
                }

                Debug.WriteLine($"[INFO] 检查文件是否可写入: {filePath}");

                // 尝试以写入模式打开文件
                using (FileStream fs = File.OpenWrite(filePath))
                {
                    // 只是测试写入权限，不实际写入内容
                    Debug.WriteLine("[INFO] 文件可写入");
                    return true;
                }
            }
            catch (UnauthorizedAccessException)
            {
                Debug.WriteLine("[WARN] 文件无写入权限");
                return false;
            }
            catch (IOException ex)
            {
                Debug.WriteLine($"[WARN] 文件被占用或其他IO错误: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[WARN] 检查文件写入权限时出错: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 确保文件可写入（移除只读属性）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件是否可写入</returns>
        public static bool EnsureFileWritable(string filePath)
        {
            try
            {
                // Debug.WriteLine($"[INFO] 确保文件可写入: {filePath}");

                // 移除只读属性（如果文件不是只读，一般就是可写入的）
                if (!RemoveReadOnlyAttribute(filePath))
                {
                    Debug.WriteLine($"[ERROR] 无法移除文件只读属性: {filePath}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 确保文件可写入失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查文件是否为只读
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件是否为只读</returns>
        public static bool IsFileReadOnly(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                {
                    return false;
                }

                if (OperatingSystem.IsWindows())
                {
                    FileAttributes attributes = File.GetAttributes(filePath);
                    return (attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly;
                }
                else
                {
                    // Unix系统通过文件权限检查（避免调用IsFileWritable）
                    try
                    {
                        var fileInfo = new FileInfo(filePath);
                        // 检查当前用户是否有写权限
                        return !fileInfo.IsReadOnly;
                    }
                    catch
                    {
                        // 如果无法检查权限，假设不是只读
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 检查文件只读状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取文件权限信息（调试用）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>权限信息字符串</returns>
        public static string GetFilePermissionInfo(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                {
                    return "文件不存在";
                }

                if (OperatingSystem.IsWindows())
                {
                    FileAttributes attributes = File.GetAttributes(filePath);
                    return $"属性: {attributes}";
                }
                else
                {
                    // Unix系统可以通过stat命令获取详细权限
                    using (var process = new Process())
                    {
                        process.StartInfo.FileName = "stat";
                        process.StartInfo.Arguments = $"-c '%a %n' \"{filePath}\"";
                        process.StartInfo.UseShellExecute = false;
                        process.StartInfo.RedirectStandardOutput = true;
                        process.StartInfo.CreateNoWindow = true;
                        process.Start();

                        string output = process.StandardOutput.ReadToEnd();
                        process.WaitForExit();

                        return string.IsNullOrEmpty(output) ? "无法获取权限信息" : output.Trim();
                    }
                }
            }
            catch (Exception ex)
            {
                return $"获取权限信息失败: {ex.Message}";
            }
        }

        #region 平台特定实现

        /// <summary>
        /// Windows版本的移除只读属性
        /// </summary>
        [SupportedOSPlatform("windows")]
        private static bool RemoveReadOnlyAttributeWindows(string filePath)
        {
            try
            {
                FileAttributes attributes = File.GetAttributes(filePath);
                if ((attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly)
                {
                    File.SetAttributes(filePath, attributes & ~FileAttributes.ReadOnly);
                    Debug.WriteLine($"[INFO] 已移除Windows文件只读属性: {filePath}");
                }
                else
                {
                    Debug.WriteLine($"[INFO] Windows文件本身不是只读: {filePath}");
                }
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] Windows移除只读属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Mac版本的移除只读属性
        /// </summary>
        [SupportedOSPlatform("macos")]
        private static bool RemoveReadOnlyAttributeMac(string filePath)
        {
            try
            {
                using (var process = new Process())
                {
                    process.StartInfo.FileName = "chmod";
                    process.StartInfo.Arguments = $"644 \"{filePath}\"";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardError = true;
                    process.StartInfo.CreateNoWindow = true;
                    process.Start();

                    string error = process.StandardError.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        Debug.WriteLine($"[INFO] 已移除Mac文件只读属性: {filePath}");
                        return true;
                    }
                    else
                    {
                        Debug.WriteLine($"[ERROR] Mac移除只读属性失败: {error}");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] Mac移除只读属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Linux版本的移除只读属性
        /// </summary>
        [SupportedOSPlatform("linux")]
        private static bool RemoveReadOnlyAttributeLinux(string filePath)
        {
            try
            {
                using (var process = new Process())
                {
                    process.StartInfo.FileName = "chmod";
                    process.StartInfo.Arguments = $"644 \"{filePath}\"";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardError = true;
                    process.StartInfo.CreateNoWindow = true;
                    process.Start();

                    string error = process.StandardError.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        Debug.WriteLine($"[INFO] 已移除Linux文件只读属性: {filePath}");
                        return true;
                    }
                    else
                    {
                        Debug.WriteLine($"[ERROR] Linux移除只读属性失败: {error}");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] Linux移除只读属性失败: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
