﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    <ApplicationIcon>Assets/织梦logo.ico</ApplicationIcon>
    <PublishSingleFile>true</PublishSingleFile>
    <IncludeAllContentForSelfExtract>true</IncludeAllContentForSelfExtract>
    <AssemblyName>CursorPro</AssemblyName>
    <SelfContained>true</SelfContained>

    <!-- Mac Bundle ID 配置 -->
    <CFBundleIdentifier>com.cursorpro.app</CFBundleIdentifier>
    <CFBundleName>CursorPro</CFBundleName>
    <CFBundleDisplayName>CursorPro</CFBundleDisplayName>
    <CFBundleVersion>1.0.0</CFBundleVersion>
    <CFBundleShortVersionString>1.0.0</CFBundleShortVersionString>

  </PropertyGroup>

  <PropertyGroup Condition="'$(OS)' == 'Windows_NT'">
    <DefineConstants>WINDOWS</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <AvaloniaResource Include="Assets\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Assets/织梦logo.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.2.1" />
    <PackageReference Include="Avalonia.Desktop" Version="11.2.1" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.2.1" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.2.1" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Include="Avalonia.Diagnostics" Version="11.2.1">
      <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
      <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
    </PackageReference>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.1" />
    <PackageReference Include="Material.Avalonia" Version="3.10.1" />
    <PackageReference Include="MessageBox.Avalonia" Version="3.2.0" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.4" />
  </ItemGroup>
</Project>
