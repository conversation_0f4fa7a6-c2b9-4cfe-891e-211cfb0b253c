using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Data.Core.Plugins;
using Avalonia.Markup.Xaml;
using System;
using System.Diagnostics;
using System.Linq;

namespace CursorPro
{
    public partial class App : Application
    {

        public override void Initialize()
        {
            AvaloniaXamlLoader.Load(this);
        }

        public override void OnFrameworkInitializationCompleted()
        {
            if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                // Avoid duplicate validations from both Avalonia and the CommunityToolkit.
                // More info: https://docs.avaloniaui.net/docs/guides/development-guides/data-validation#manage-validationplugins
                DisableAvaloniaDataAnnotationValidation();

                // 检查是否已有实例在运行
                if (!ViewModels.Mutex.Initialize())
                {
                    Debug.WriteLine("已有实例运行，退出");
                    return; // 如果已有实例运行，则退出
                }

                // 直接创建并显示主要窗口，无需验证
                desktop.MainWindow = new Views.主要窗口
                {
                    DataContext = new ViewModels.主要窗口(),
                };

                // 显示主窗口
                desktop.MainWindow.Show();



                // 注册应用程序退出事件
                desktop.ShutdownRequested += Desktop_ShutdownRequested;
            }

            base.OnFrameworkInitializationCompleted();
        }


        // 应用程序退出事件处理
        private void Desktop_ShutdownRequested(object? sender, ShutdownRequestedEventArgs e)
        {
            Debug.WriteLine("应用程序正在退出...");

            // 确保主要窗口的资源被正确清理
            if (sender is IClassicDesktopStyleApplicationLifetime desktop &&
                desktop.MainWindow?.DataContext is IDisposable disposableViewModel)
            {
                try
                {
                    disposableViewModel.Dispose();
                    Debug.WriteLine("主要窗口ViewModel资源已清理");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"清理主要窗口ViewModel资源时出错: {ex.Message}");
                }
            }
        }


        private void DisableAvaloniaDataAnnotationValidation()
        {
            // Get an array of plugins to remove
            var dataValidationPluginsToRemove =
                BindingPlugins.DataValidators.OfType<DataAnnotationsValidationPlugin>().ToArray();

            // remove each entry found
            foreach (var plugin in dataValidationPluginsToRemove)
            {
                BindingPlugins.DataValidators.Remove(plugin);
            }
        }
    }
}