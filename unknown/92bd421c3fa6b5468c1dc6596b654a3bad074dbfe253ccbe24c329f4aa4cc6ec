namespace CursorPro.Models;

using Avalonia.Threading;

using MsBox.Avalonia;
using MsBox.Avalonia.Enums;
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

public class Tools
{
    /// <summary>
    /// 打开指定的URL
    /// </summary>
    /// <param name="url">要打开的网址</param>
    /// <returns>是否成功打开</returns>
    public static bool OpenUrl(string url)
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                // Windows平台使用默认浏览器打开
                Process.Start(new ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                // Mac平台使用open命令
                Process.Start("open", url);
            }

            else
            {
                // 其他平台尝试使用默认方式
                Process.Start(new ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                });
            }
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"打开URL时发生错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 在主线程显示消息框
    /// </summary>
    /// <param name="标题">消息框标题</param>
    /// <param name="内容">消息框内容</param>
    /// <param name="按钮类型">按钮类型，默认为确定按钮</param>
    /// <param name="图标类型">图标类型，默认为信息图标</param>
    /// <returns>用户点击的按钮结果</returns>
    public static void 显示消息框(string 标题, string 内容, ButtonEnum 按钮类型 = ButtonEnum.Ok, Icon 图标类型 = Icon.Info)
    {
        // 检查是否在UI线程上
        if (Dispatcher.UIThread.CheckAccess())
        {
            // 已经在UI线程上，使用同步方式显示对话框
            var messageBox = MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型);
            messageBox.ShowAsync().Wait(); // 使用Wait()等待对话框关闭
        }
        else
        {
            // 不在UI线程上，切换到UI线程并等待完成
            Dispatcher.UIThread.InvokeAsync(() =>
            {
                var messageBox = MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型);
                return messageBox.ShowAsync();
            }).Wait();
        }
    }

    /// <summary>
    /// 在主线程显示消息框（异步版本）
    /// </summary>
    /// <param name="标题">消息框标题</param>
    /// <param name="内容">消息框内容</param>
    /// <param name="按钮类型">按钮类型，默认为确定按钮</param>
    /// <param name="图标类型">图标类型，默认为信息图标</param>
    /// <returns>表示异步操作的任务</returns>
    public static async Task 显示消息框Async(string 标题, string 内容, ButtonEnum 按钮类型 = ButtonEnum.Ok, Icon 图标类型 = Icon.Info)
    {
        // 检查是否在UI线程上
        if (Dispatcher.UIThread.CheckAccess())
        {
            // 已经在UI线程上，直接显示
            var messageBox = MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型);
            await messageBox.ShowAsync();
        }
        else
        {
            // 不在UI线程上，切换到UI线程
            await Dispatcher.UIThread.InvokeAsync(async () =>
            {
                var messageBox = MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型);
                await messageBox.ShowAsync();
            });
        }
    }

    /// <summary>
    /// 在主线程显示消息框并返回用户选择结果（异步版本）
    /// </summary>
    /// <param name="标题">消息框标题</param>
    /// <param name="内容">消息框内容</param>
    /// <param name="按钮类型">按钮类型，默认为确定按钮</param>
    /// <param name="图标类型">图标类型，默认为信息图标</param>
    /// <returns>用户点击的按钮结果</returns>
    public static async Task<ButtonResult> 显示确认消息框Async(string 标题, string 内容, ButtonEnum 按钮类型 = ButtonEnum.Ok, Icon 图标类型 = Icon.Info)
    {
        // 检查是否在UI线程上
        if (Dispatcher.UIThread.CheckAccess())
        {
            // 已经在UI线程上，直接显示
            var messageBox = MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型);
            return await messageBox.ShowAsync();
        }
        else
        {
            // 不在UI线程上，切换到UI线程
            return await Dispatcher.UIThread.InvokeAsync(async () =>
            {
                var messageBox = MessageBoxManager.GetMessageBoxStandard(标题, 内容, 按钮类型, 图标类型);
                return await messageBox.ShowAsync();
            });
        }
    }

}




