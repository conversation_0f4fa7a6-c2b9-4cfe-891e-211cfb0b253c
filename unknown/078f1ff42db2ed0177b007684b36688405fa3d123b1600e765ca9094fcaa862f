using CursorPro.Models;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace CursorPro.ViewModels
{
    /// <summary>
    /// 虚假获取处理类
    /// 用于处理免费用户的虚假获取，显示虚假的成功提示
    /// 虚假获取逻辑现在由服务端控制，客户端只负责执行虚假获取的UI效果
    /// </summary>
    public static class 免费虚假获取
    {

        /// <summary>
        /// 执行虚假获取（显示虚假的成功提示）
        /// </summary>
        /// <param name="设置状态文本">用于设置状态文本的回调函数</param>
        public static async Task 执行免费虚假获取(Action<string> 设置状态文本)
        {
            try
            {
                Debug.WriteLine("开始执行虚假获取");

                // 获取状态文本变成空的
                设置状态文本("");

                //延时200毫秒
                await Task.Delay(200);

                // 立即显示成功消息
                设置状态文本("获取成功! 和cursor继续对话吧");

                Debug.WriteLine("虚假获取完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"执行虚假获取时出错: {ex.Message}");
                设置状态文本("获取失败");
            }
        }

        /// <summary>
        /// 执行付费虚假获取（显示虚假的成功提示）
        /// </summary>
        /// <param name="设置状态文本">用于设置状态文本的回调函数</param>
        public static async Task 执行付费虚假获取(Action<string> 设置状态文本)
        {
            try
            {
                Debug.WriteLine("开始执行付费虚假获取");

                // 获取状态文本变成空的
                设置状态文本("");

                //延时200毫秒
                await Task.Delay(200);

                // 立即显示成功消息
                设置状态文本("获取成功! 和cursor继续对话吧");

                Debug.WriteLine("付费虚假获取完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"执行付费虚假获取时出错: {ex.Message}");
                设置状态文本("获取失败");
            }
        }

        /// <summary>
        /// 检查是否为免费用户
        /// </summary>
        /// <returns>是否为免费用户</returns>
        public static bool 是否为免费用户()
        {
            try
            {
                string? 本地卡密 = LocalConfig.读取("CardKey");
                bool 是免费用户 = string.IsNullOrEmpty(本地卡密);

                Debug.WriteLine($"用户类型检查: {(是免费用户 ? "免费用户" : "付费用户")}");

                return 是免费用户;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查用户类型时出错: {ex.Message}");
                return true; // 出错时默认为免费用户
            }
        }
    }
}
