using CursorPro.Models;
using System;
using System.Diagnostics;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace CursorPro.ViewModels
{
    /// <summary>
    /// 检查限制类，用于验证用户的使用权限和限制
    /// 包括试用验证、付费验证、启动检查等
    /// </summary>
    public class 检查限制
    {
        private readonly 获取设备标识 _设备标识 = new();

        /// <summary>
        /// 验证试用账号
        /// </summary>
        /// <returns>验证结果</returns>
        public async Task<验证结果> 验证试用账号()
        {
            try
            {
                string 机器码 = _设备标识.获取机器码();

                var 请求数据 = new
                {
                    operation = "verify_trial",
                    machine_code = 机器码,
                    version = Info.本地版本号,
                    platform = Info.平台类型
                };

                // 序列化请求数据以便调试
                string 请求Json = JsonSerializer.Serialize(请求数据);
                Debug.WriteLine($"【关键信息】完整请求数据: {请求Json}");

                // 使用HttpService发送请求
                try
                {
                    var apiUrl = $"{Info.API基础地址}:{Info.免费服务端口}/api/account";
                    var 响应数据 = await HttpService.PostAsync<object, 验证结果>(apiUrl, 请求数据);

                    // 优先检查版本更新
                    if (响应数据?.NeedUpdate == true)
                    {
                        Debug.WriteLine("免费验证检测到版本过低，需要更新");
                        处理版本更新();
                        return 响应数据;
                    }

                    return 响应数据 ?? new 验证结果 { Success = false, Message = "解析响应失败" };
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"验证试用账号请求出错: {ex.Message}");
                    return 创建失败结果("网络连接失败，请检查网络后重试");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证试用账号出错: {ex.Message}");
                return 创建失败结果($"验证出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证付费账号
        /// </summary>
        /// <param name="卡密">卡密</param>
        /// <returns>验证结果</returns>
        public async Task<验证结果> 验证付费账号(string 卡密)
        {
            try
            {
                string 机器码 = _设备标识.获取机器码();

                var 请求数据 = new
                {
                    operation = "verify_paid",
                    key = 卡密,
                    machine_code = 机器码,
                    version = Info.本地版本号,
                    platform = Info.平台类型,
                    app_name = Info.AppName,
                    remark = Info.代理
                };

                // 打印请求参数用于调试
                string 请求Json = JsonSerializer.Serialize(请求数据);
                Debug.WriteLine($"请求参数: {请求Json}");

                // 使用HttpService发送请求
                try
                {
                    var apiUrl = $"{Info.API基础地址}:{Info.付费服务端口}/api/paid";
                    // 打印完整URL用于调试
                    Debug.WriteLine($"请求URL: {apiUrl}");

                    var 响应数据 = await HttpService.PostAsync<object, 验证结果>(apiUrl, 请求数据);

                    // 优先检查版本更新
                    if (响应数据?.NeedUpdate == true)
                    {
                        Debug.WriteLine("付费验证检测到版本过低，需要更新");
                        处理版本更新();
                        return 响应数据;
                    }

                    // 如果成功，保存卡密信息到本地配置
                    if (响应数据?.Success == true)
                    {
                        LocalConfig.写入("CardKey", 卡密);

                        // 保存到期时间到本地配置和Info变量（避免重装系统后丢失）
                        if (!string.IsNullOrEmpty(响应数据.ExpiryTime))
                        {
                            LocalConfig.写入("ExpiryTime", 响应数据.ExpiryTime);
                            Info.到期时间 = 响应数据.ExpiryTime;
                            Debug.WriteLine($"付费验证成功，保存到期时间: {响应数据.ExpiryTime}");
                        }
                    }

                    return 响应数据 ?? new 验证结果 { Success = false, Message = "解析响应失败" };
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"验证付费账号请求出错: {ex.Message}");
                    return 创建失败结果("网络连接失败，请检查网络后重试");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"验证付费账号出错: {ex.Message}");
                return 创建失败结果($"验证出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动检查 - 统一验证入口
        /// </summary>
        /// <returns>验证结果</returns>
        public async Task<验证结果> 启动检查()
        {
            try
            {
                // 检查本地订单
                var 购买VM = new 购买卡密VM();
                var 本地订单结果 = await 购买VM.检查并处理本地订单();
                if (本地订单结果 != null)
                {
                    Debug.WriteLine("检测到支付成功，继续执行验证获取完整账号信息");
                }

                // 执行验证
                return await 执行验证();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"启动检查出错: {ex.Message}");
                return 创建失败结果($"启动检查出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行验证逻辑
        /// </summary>
        private async Task<验证结果> 执行验证()
        {
            string? 本地卡密 = LocalConfig.读取("CardKey");

            if (!string.IsNullOrEmpty(本地卡密))
            {
                Debug.WriteLine("检测到本地卡密，执行付费账号验证");
                return await 验证付费账号(本地卡密);
            }
            else
            {
                Debug.WriteLine("未检测到本地卡密，执行免费验证");
                return await 验证试用账号();
            }
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        private static 验证结果 创建失败结果(string 错误消息)
        {
            return new 验证结果
            {
                Success = false,
                Message = 错误消息
            };
        }

        /// <summary>
        /// 处理版本更新
        /// </summary>
        private void 处理版本更新()
        {
            try
            {
                string 下载链接 = Info.获取下载链接();
                Tools.OpenUrl(下载链接);
                Debug.WriteLine($"已处理版本更新，平台类型: {Info.平台类型}，下载链接: {下载链接}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"处理版本更新时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 验证结果类
    /// </summary>
    public class 验证结果
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("trial_count")]
        public int? TrialCount { get; set; }

        [JsonPropertyName("need_update")]
        public bool? NeedUpdate { get; set; }

        [JsonPropertyName("latest_version")]
        public string? LatestVersion { get; set; }

        [JsonPropertyName("account_info")]
        public 账号信息? AccountInfo { get; set; }

        [JsonPropertyName("key_info")]
        public JsonElement? KeyInfo { get; set; }

        [JsonPropertyName("expiry_time")]
        public string? ExpiryTime { get; set; }

        [JsonPropertyName("is_fake")]
        public bool? IsFake { get; set; }
    }

    /// <summary>
    /// 账号信息类
    /// </summary>
    public class 账号信息
    {
        [JsonPropertyName("邮箱")]
        public string? 邮箱 { get; set; }

        [JsonPropertyName("访问令牌")]
        public string? 访问令牌 { get; set; }

        [JsonPropertyName("刷新令牌")]
        public string? 刷新令牌 { get; set; }
    }
}