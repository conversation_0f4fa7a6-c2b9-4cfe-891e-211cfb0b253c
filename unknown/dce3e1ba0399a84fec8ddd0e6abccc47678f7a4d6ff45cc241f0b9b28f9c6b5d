using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;

namespace CursorPro.Models
{
    /// <summary>
    /// Cursor进程管理器 - 跨平台通用类
    /// 提供关闭和启动Cursor应用的功能
    /// </summary>
    public static class CursorProcessManager
    {
        // 缓存的Cursor路径
        private static string? _cachedCursorPath;

        // 缓存的Cursor安装目录
        private static string? _cachedInstallPath;

        /// <summary>
        /// 初始化Cursor路径缓存
        /// </summary>
        public static void InitializeCursorPathCache()
        {
            try
            {
                _cachedCursorPath = GetCursorPath();
                if (!string.IsNullOrEmpty(_cachedCursorPath))
                {
                    Debug.WriteLine($"[INFO] 启动时缓存Cursor路径: {_cachedCursorPath}");
                }
                else
                {
                    Debug.WriteLine("[INFO] 启动时未找到Cursor路径，将在需要时重新检测");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 初始化Cursor路径缓存失败: {ex.Message}");
                _cachedCursorPath = null;
            }
        }

        /// <summary>
        /// 获取Cursor路径（优先使用缓存，路径失效时重新检测）
        /// </summary>
        public static string? GetCachedCursorPath()
        {
            // 如果有缓存且路径存在，直接返回
            if (!string.IsNullOrEmpty(_cachedCursorPath) && File.Exists(_cachedCursorPath))
            {
                return _cachedCursorPath;
            }

            // 缓存失效，重新检测
            Debug.WriteLine("[INFO] 缓存的Cursor路径失效，重新检测...");
            _cachedCursorPath = GetCursorPath();

            if (!string.IsNullOrEmpty(_cachedCursorPath))
            {
                Debug.WriteLine($"[INFO] 重新检测到Cursor路径: {_cachedCursorPath}");
            }

            return _cachedCursorPath;
        }

        /// <summary>
        /// 获取Cursor安装目录（优先使用缓存）
        /// </summary>
        public static string? GetCachedInstallPath()
        {
            // 检查缓存的安装路径是否有效
            if (!string.IsNullOrEmpty(_cachedInstallPath) && Directory.Exists(_cachedInstallPath))
            {
                // 进一步验证：检查Cursor.exe是否还在这个目录中
                string expectedCursorPath = Path.Combine(_cachedInstallPath, "Cursor.exe");
                if (File.Exists(expectedCursorPath))
                {
                    return _cachedInstallPath;
                }
                else
                {
                    Debug.WriteLine($"[WARN] 缓存的安装目录中Cursor.exe不存在，清除缓存: {_cachedInstallPath}");
                    _cachedInstallPath = null;
                }
            }

            // 从Cursor路径推导安装目录
            string? cursorPath = GetCachedCursorPath();
            if (!string.IsNullOrEmpty(cursorPath))
            {
                _cachedInstallPath = Path.GetDirectoryName(cursorPath);
                Debug.WriteLine($"[INFO] 重新缓存Cursor安装目录: {_cachedInstallPath}");
                return _cachedInstallPath;
            }

            Debug.WriteLine("[WARN] 无法获取有效的Cursor安装目录");
            return null;
        }

        /// <summary>
        /// 关闭所有Cursor进程
        /// </summary>
        /// <returns>是否成功关闭</returns>
        public static async Task<bool> CloseCursorAsync()
        {
            try
            {
                Debug.WriteLine("[INFO] 开始关闭Cursor进程...");

                if (OperatingSystem.IsWindows())
                {
                    return CloseWindowsCursor();
                }
                else if (OperatingSystem.IsMacOS())
                {
                    return await CloseMacCursorAsync();
                }
                else
                {
                    Debug.WriteLine("[ERROR] 不支持的操作系统");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 关闭Cursor进程失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取Cursor应用路径
        /// </summary>
        /// <returns>Cursor应用路径，如果未找到则返回null</returns>
        public static string? GetCursorPath()
        {
            try
            {
                Debug.WriteLine("[INFO] 开始获取Cursor应用路径...");

                if (OperatingSystem.IsWindows())
                {
                    return GetDefaultWindowsCursorPath();
                }
                else if (OperatingSystem.IsMacOS())
                {
                    return GetDefaultMacCursorPath();
                }
                else
                {
                    Debug.WriteLine("[ERROR] 不支持的操作系统");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 获取Cursor路径异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 启动Cursor应用
        /// </summary>
        /// <param name="cursorPath">Cursor应用路径（可选，如果为空则使用默认路径）</param>
        /// <returns>是否成功启动</returns>
        public static async Task<bool> StartCursorAsync(string? cursorPath = null)
        {
            try
            {
                Debug.WriteLine("[INFO] 开始启动Cursor应用...");

                if (OperatingSystem.IsWindows())
                {
                    return StartWindowsCursor(cursorPath);
                }
                else if (OperatingSystem.IsMacOS())
                {
                    return await StartMacCursorAsync(cursorPath);
                }
                else
                {
                    Debug.WriteLine("[ERROR] 不支持的操作系统");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 启动Cursor应用失败: {ex.Message}");
                return false;
            }
        }

        #region Windows平台实现

        /// <summary>
        /// 关闭Windows平台的Cursor进程
        /// </summary>
        private static bool CloseWindowsCursor()
        {
            try
            {
                Debug.WriteLine("[INFO] 正在关闭Windows平台Cursor进程...");

                // 获取所有Cursor进程
                Process[] processes = Process.GetProcessesByName("Cursor");
                if (processes.Length == 0)
                {
                    Debug.WriteLine("[INFO] 未发现运行中的Cursor进程");
                    return true;
                }

                Debug.WriteLine($"[INFO] 发现 {processes.Length} 个Cursor进程，正在关闭...");

                // 尝试优雅关闭
                // foreach (Process process in processes)
                // {
                //     try
                //     {
                //         if (!process.HasExited)
                //         {
                //             process.CloseMainWindow();
                //             Debug.WriteLine($"[INFO] 已发送关闭信号到进程 {process.Id}");
                //         }
                //     }
                //     catch (Exception ex)
                //     {
                //         Debug.WriteLine($"[WARN] 优雅关闭进程 {process.Id} 失败: {ex.Message}");
                //     }
                // }

                // 等待进程关闭
                // 移除延时，直接检查进程状态

                // 检查是否还有进程运行，如果有则强制终止
                processes = Process.GetProcessesByName("Cursor");
                if (processes.Length > 0)
                {
                    Debug.WriteLine($"[INFO] 有 {processes.Length} 个进程运行，强制终止...");
                    foreach (Process process in processes)
                    {
                        try
                        {
                            if (!process.HasExited)
                            {
                                process.Kill();
                                // Debug.WriteLine($"[INFO] 已强制终止进程 {process.Id}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"[WARN] 强制终止进程失败: {ex.Message}");
                        }
                    }
                }

                // 动态检查进程状态 - 一旦所有进程退出就立即继续
                int runningProcessCount = 0;
                int maxCheckAttempts = 20; // 最多检查20次（约2秒）
                int checkInterval = 100; // 每次检查间隔100毫秒

                for (int attempt = 0; attempt < maxCheckAttempts; attempt++)
                {
                    runningProcessCount = 0;
                    processes = Process.GetProcessesByName("Cursor");

                    // 检查进程是否真的还在运行（而不是僵尸进程）
                    foreach (Process process in processes)
                    {
                        try
                        {
                            if (!process.HasExited)
                            {
                                runningProcessCount++;
                            }
                        }
                        catch (Exception)
                        {
                            // 进程已经退出，忽略异常
                        }
                    }

                    // 如果没有运行中的进程，立即退出循环
                    if (runningProcessCount == 0)
                    {
                        Debug.WriteLine($"[INFO] 所有进程已退出，检查用时: {attempt * checkInterval}毫秒");
                        break;
                    }

                    // 如果还有进程在运行，等待一小段时间再检查
                    if (attempt < maxCheckAttempts - 1) // 最后一次不需要等待
                    {
                        Thread.Sleep(checkInterval);
                    }
                }

                bool success = runningProcessCount == 0;
                if (!success)
                {
                    Debug.WriteLine($"[WARN] 仍有 {runningProcessCount} 个进程在运行");
                }
                Debug.WriteLine($"[INFO] Windows平台Cursor进程关闭{(success ? "成功" : "失败")}");
                return success;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 关闭Windows平台Cursor进程异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 启动Windows平台的Cursor应用
        /// </summary>
        private static bool StartWindowsCursor(string? cursorPath)
        {
            try
            {
                // 如果没有提供路径，尝试获取默认路径
                if (string.IsNullOrEmpty(cursorPath))
                {
                    cursorPath = GetDefaultWindowsCursorPath();
                    if (string.IsNullOrEmpty(cursorPath))
                    {
                        Debug.WriteLine("[ERROR] 无法找到Cursor应用路径");
                        return false;
                    }
                }

                Debug.WriteLine($"[INFO] 正在启动Windows平台Cursor: {cursorPath}");

                // 方法1: 尝试以普通用户权限启动
                bool success = StartCursorAsNormalUser(cursorPath);
                if (success)
                {
                    Debug.WriteLine("[INFO] 已以普通用户权限启动Cursor");
                    return true;
                }

                Debug.WriteLine("[WARN] 普通用户权限启动失败，尝试直接启动");

                // 方法2: 直接启动
                var startInfo = new ProcessStartInfo
                {
                    FileName = cursorPath,
                    UseShellExecute = true
                };

                Process.Start(startInfo);
                Debug.WriteLine("[INFO] 已启动Cursor（可能继承当前权限）");

                // 移除启动延时
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 启动Windows平台Cursor失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 以普通用户权限启动Cursor（从管理员权限的进程中）
        /// </summary>
        private static bool StartCursorAsNormalUser(string cursorPath)
        {
            try
            {
                Debug.WriteLine("[INFO] 尝试以普通用户权限启动Cursor...");

                // 方法1: 使用explorer.exe启动
                var startInfo = new ProcessStartInfo
                {
                    FileName = "explorer.exe",
                    Arguments = $"\"{cursorPath}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                var process = Process.Start(startInfo);
                if (process != null)
                {
                    Debug.WriteLine("[INFO] 通过explorer.exe启动成功");
                    return true;
                }

                // 方法2: 使用cmd /c start命令
                var startInfo2 = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c start \"\" \"{cursorPath}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                var process2 = Process.Start(startInfo2);
                if (process2 != null)
                {
                    Debug.WriteLine("[INFO] 通过cmd start命令启动成功");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 以普通用户权限启动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取Windows平台默认Cursor路径
        /// </summary>
        private static string? GetDefaultWindowsCursorPath()
        {
            try
            {
                Debug.WriteLine("[INFO] 开始搜索Cursor应用路径...");

                // 方法1: 优先从缓存变量读取（性能最佳）
                if (!string.IsNullOrEmpty(_cachedCursorPath) && File.Exists(_cachedCursorPath))
                {
                    Debug.WriteLine($"[INFO] 从缓存变量读取到有效的Cursor路径: {_cachedCursorPath}");
                    return _cachedCursorPath;
                }

                // 方法2: 从本地配置读取
                string? configPath = LocalConfig.读取("cursor_path");
                if (!string.IsNullOrEmpty(configPath) && File.Exists(configPath))
                {
                    Debug.WriteLine($"[INFO] 从LocalConfig读取到有效的Cursor路径: {configPath}");
                    // 更新缓存变量
                    _cachedCursorPath = configPath;
                    return configPath;
                }
                else if (!string.IsNullOrEmpty(configPath))
                {
                    Debug.WriteLine($"[WARN] LocalConfig中的路径不存在: {configPath}，将清空配置");
                    LocalConfig.写入("cursor_path", "");
                }

                // 方法3: 从运行中的进程获取
                Process[] processes = Process.GetProcessesByName("Cursor");
                if (processes.Length > 0)
                {
                    string? processPath = processes[0].MainModule?.FileName;
                    if (!string.IsNullOrEmpty(processPath) && File.Exists(processPath))
                    {
                        Debug.WriteLine($"[INFO] 从运行进程找到Cursor路径: {processPath}");
                        // 自动保存到配置文件和缓存变量
                        LocalConfig.写入("cursor_path", processPath);
                        _cachedCursorPath = processPath;
                        return processPath;
                    }
                }

                // 方法4: 从桌面快捷方式获取
                string? desktopPath = GetCursorPathFromDesktop();
                if (!string.IsNullOrEmpty(desktopPath))
                {
                    // 自动保存到配置文件和缓存变量
                    LocalConfig.写入("cursor_path", desktopPath);
                    _cachedCursorPath = desktopPath;
                    return desktopPath;
                }

                // 移除开始菜单检测方式以避免杀毒软件误报

                Debug.WriteLine("[WARN] 未找到Cursor应用路径");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 搜索Cursor路径时发生异常: {ex.Message}");
                return null;
            }
        }





        /// <summary>
        /// 从桌面快捷方式获取Cursor路径
        /// </summary>
        private static string? GetCursorPathFromDesktop()
        {
            try
            {
                Debug.WriteLine("[INFO] 开始从桌面快捷方式搜索Cursor路径...");

                // 直接检查桌面快捷方式，避免递归扫描
                var desktopShortcuts = new[]
                {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "Cursor.lnk"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonDesktopDirectory), "Cursor.lnk")
                };

                foreach (string shortcutPath in desktopShortcuts)
                {
                    try
                    {
                        if (File.Exists(shortcutPath))
                        {
                            Debug.WriteLine($"[INFO] 找到桌面快捷方式: {shortcutPath}");
                            string? targetPath = GetShortcutTarget(shortcutPath);
                            if (!string.IsNullOrEmpty(targetPath) && File.Exists(targetPath))
                            {
                                Debug.WriteLine($"[SUCCESS] 从桌面快捷方式获取到Cursor路径: {targetPath}");
                                return targetPath;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"[WARN] 检查桌面快捷方式失败 {shortcutPath}: {ex.Message}");
                        continue;
                    }
                }

                Debug.WriteLine("[INFO] 桌面快捷方式中未找到Cursor");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 从桌面快捷方式搜索Cursor路径时发生异常: {ex.Message}");
                return null;
            }
        }





        /// <summary>
        /// 获取快捷方式的目标路径
        /// </summary>
        private static string? GetShortcutTarget(string shortcutPath)
        {
            try
            {
                // 使用简单的方法读取.lnk文件
                // 这里使用一个简化的实现，适用于大多数情况
                if (OperatingSystem.IsWindows())
                {
                    return GetShortcutTargetWindows(shortcutPath);
                }
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 解析快捷方式失败 {shortcutPath}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Windows平台获取快捷方式目标路径
        /// </summary>
        private static string? GetShortcutTargetWindows(string shortcutPath)
        {
            // 确保只在Windows平台上执行
            if (!OperatingSystem.IsWindows())
            {
                return null;
            }

            try
            {
                // 使用WScript.Shell COM对象来解析快捷方式
                Type? shellType = Type.GetTypeFromProgID("WScript.Shell");
                if (shellType == null) return null;

                dynamic? shell = Activator.CreateInstance(shellType);
                if (shell == null) return null;

                var shortcut = shell.CreateShortcut(shortcutPath);
                string targetPath = shortcut.TargetPath;

                // 释放COM对象
                if (shortcut != null) Marshal.ReleaseComObject(shortcut);
                if (shell != null) Marshal.ReleaseComObject(shell);

                return string.IsNullOrEmpty(targetPath) ? null : targetPath;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] Windows快捷方式解析失败: {ex.Message}");
                return null;
            }
        }







        #endregion

        #region Mac平台实现

        /// <summary>
        /// 获取Mac平台默认Cursor路径
        /// </summary>
        private static string? GetDefaultMacCursorPath()
        {
            try
            {
                Debug.WriteLine("[INFO] 开始检查Mac平台Cursor应用路径...");

                string cursorAppPath = "/Applications/Cursor.app";

                // 检查应用程序是否存在
                if (Directory.Exists(cursorAppPath))
                {
                    Debug.WriteLine($"[INFO] 找到Mac平台Cursor应用: {cursorAppPath}");
                    return cursorAppPath;
                }
                else
                {
                    Debug.WriteLine($"[ERROR] Mac平台Cursor应用不存在: {cursorAppPath}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 检查Mac平台Cursor路径时发生异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 关闭Mac平台的Cursor进程
        /// </summary>
        private static async Task<bool> CloseMacCursorAsync()
        {
            try
            {
                Debug.WriteLine("[INFO] 正在关闭Mac平台Cursor进程...");

                // 检查是否有Cursor进程
                var initialCheckResult = await ExecuteCommand("pgrep", "-f Cursor.app", "检查初始Cursor进程", false);
                if (string.IsNullOrWhiteSpace(initialCheckResult))
                {
                    Debug.WriteLine("[INFO] 未发现运行中的Cursor进程");
                    return true;
                }

                var processIds = initialCheckResult.Trim().Split('\n', StringSplitOptions.RemoveEmptyEntries);
                Debug.WriteLine($"[INFO] 发现 {processIds.Length} 个Cursor进程，正在关闭...");

                // 直接强制终止所有Cursor进程（跳过优雅关闭）
                await ExecuteCommand("pkill", "-KILL -f Cursor.app", "强制终止Cursor进程");

                // 动态检查进程状态 - 一旦所有进程退出就立即继续
                int runningProcessCount = 0;
                int maxCheckAttempts = 20; // 最多检查20次（约2秒）
                int checkInterval = 100; // 每次检查间隔100毫秒

                for (int attempt = 0; attempt < maxCheckAttempts; attempt++)
                {
                    // 检查是否还有Cursor进程
                    var checkResult = await ExecuteCommand("pgrep", "-f Cursor.app", "检查Cursor进程", false);

                    if (string.IsNullOrWhiteSpace(checkResult))
                    {
                        runningProcessCount = 0;
                        Debug.WriteLine($"[INFO] 所有进程已退出，检查用时: {attempt * checkInterval}毫秒");
                        break;
                    }
                    else
                    {
                        var remainingProcessIds = checkResult.Trim().Split('\n', StringSplitOptions.RemoveEmptyEntries);
                        runningProcessCount = remainingProcessIds.Length;
                    }

                    // 如果还有进程在运行，等待一小段时间再检查
                    if (attempt < maxCheckAttempts - 1) // 最后一次不需要等待
                    {
                        await Task.Delay(checkInterval);
                    }
                }

                bool success = runningProcessCount == 0;
                if (!success)
                {
                    Debug.WriteLine($"[WARN] 仍有 {runningProcessCount} 个进程在运行");
                }
                Debug.WriteLine($"[INFO] Mac平台Cursor进程关闭{(success ? "成功" : "失败")}");
                return success;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 关闭Mac平台Cursor进程异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 启动Mac平台的Cursor应用
        /// </summary>
        private static async Task<bool> StartMacCursorAsync(string? cursorPath)
        {
            try
            {
                Debug.WriteLine("[INFO] 正在启动Mac平台Cursor...");

                // 方法1: 使用应用程序名称启动
                try
                {
                    await ExecuteCommand("open", "-a Cursor", "启动Cursor应用");
                    Debug.WriteLine("[INFO] Mac平台Cursor启动成功（方法1）");
                    // 移除启动延时
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[WARN] 方法1启动失败: {ex.Message}");
                }

                // 方法2: 使用应用程序路径启动
                try
                {
                    string appPath = !string.IsNullOrEmpty(cursorPath) ? cursorPath : "/Applications/Cursor.app";
                    await ExecuteCommand("open", $"\"{appPath}\"", "启动Cursor应用");
                    Debug.WriteLine("[INFO] Mac平台Cursor启动成功（方法2）");
                    // 移除启动延时
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[ERROR] 方法2启动也失败: {ex.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 启动Mac平台Cursor失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行Mac命令
        /// </summary>
        private static async Task<string> ExecuteCommand(string command, string arguments, string description, bool throwOnError = true)
        {
            try
            {
                Debug.WriteLine($"[INFO] {description}: {command} {arguments}");

                var startInfo = new ProcessStartInfo
                {
                    FileName = command,
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process == null)
                {
                    throw new Exception($"无法启动进程: {command}");
                }

                await process.WaitForExitAsync();
                string output = await process.StandardOutput.ReadToEndAsync();
                string error = await process.StandardError.ReadToEndAsync();

                if (process.ExitCode != 0 && throwOnError)
                {
                    throw new Exception($"命令执行失败 (退出码: {process.ExitCode}): {error}");
                }

                return output;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] {description}失败: {ex.Message}");
                if (throwOnError) throw;
                return string.Empty;
            }
        }

        #endregion
    }
}
