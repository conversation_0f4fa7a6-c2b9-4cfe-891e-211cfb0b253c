using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using CursorPro.ViewModels;
using System;
using System.Diagnostics;

namespace CursorPro.Views
{
    public partial class 购买卡密 : Window
    {
        private 购买卡密VM? _viewModel;

        public 购买卡密()
        {
            InitializeComponent();
            _viewModel = new 购买卡密VM();
            DataContext = _viewModel;

            // 设置窗口标题
            Title = "赞助支持";

            // 添加窗口关闭事件
            Closed += 购买卡密_Closed;
        }

        private void 购买卡密_Closed(object? sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("购买卡密窗口正在关闭");

                _viewModel = null;
                DataContext = null;

                Debug.WriteLine("购买卡密窗口关闭完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"窗口关闭事件处理时出错: {ex.Message}");
            }
        }

        private void Border_PointerPressed(object sender, PointerPressedEventArgs e)
        {
            // 支持窗口拖动
            BeginMoveDrag(e);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Debug.WriteLine("用户点击关闭按钮");

                // 清空ViewModel引用
                _viewModel = null;
                DataContext = null;
            }
            catch (Exception ex)
            {
                // 记录异常但不阻止窗口关闭
                Debug.WriteLine($"关闭窗口时出错: {ex.Message}");
            }
            finally
            {
                // 关闭窗口
                Debug.WriteLine("关闭购买窗口");
                Close();
            }
        }
    }
}